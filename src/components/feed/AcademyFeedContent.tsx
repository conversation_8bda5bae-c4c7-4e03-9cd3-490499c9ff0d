import Link from 'next/link'
import { formatRelativeTime } from '@/lib/utils'

interface AcademyFeedContentProps {
  item: {
    id: number
    type: string
    title: string
    description: string | null
    mediaUrls: string[]
    tags: string[]
    createdAt: Date | string
    contentData?: any
    user?: {
      firstName: string
      lastName: string
      avatar: string | null
    } | null
  }
}

export function AcademyFeedContent({ item }: AcademyFeedContentProps) {
  const renderAchievementContent = () => {
    const achievement = item.contentData
    if (!achievement) return null

    return (
      <div className="space-y-3">
        <div className="flex items-center space-x-3">
          <div className="w-12 h-12 rounded-full bg-gradient-to-br from-yellow-400 to-orange-500 flex items-center justify-center">
            <span className="text-2xl">🏆</span>
          </div>
          <div>
            <h3 className="font-semibold text-neutral-900 dark:text-white">
              {achievement.academyMember.user.firstName} {achievement.academyMember.user.lastName}
            </h3>
            <p className="text-sm text-neutral-600 dark:text-neutral-400">
              {achievement.academyMember.academy.name} • {achievement.academyMember.academy.sport}
            </p>
          </div>
        </div>
        
        <div className="bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 p-4 rounded-lg">
          <div className="flex items-center space-x-2 mb-2">
            <span className="text-lg">🏅</span>
            <span className="font-medium text-yellow-800 dark:text-yellow-200">
              {achievement.title}
            </span>
          </div>
          {achievement.description && (
            <p className="text-sm text-neutral-700 dark:text-neutral-300 mb-2">
              {achievement.description}
            </p>
          )}
          <div className="flex items-center space-x-4 text-xs text-neutral-600 dark:text-neutral-400">
            <span>Level: {achievement.level}</span>
            {achievement.event && (
              <span>Event: {achievement.event.title}</span>
            )}
          </div>
        </div>
      </div>
    )
  }

  const renderEventContent = () => {
    const event = item.contentData
    if (!event) return null

    return (
      <div className="space-y-3">
        <div className="flex items-center space-x-3">
          <div className="w-12 h-12 rounded-full bg-gradient-to-br from-red-400 to-pink-500 flex items-center justify-center">
            <span className="text-2xl">📅</span>
          </div>
          <div>
            <h3 className="font-semibold text-neutral-900 dark:text-white">
              {event.academy.name}
            </h3>
            <p className="text-sm text-neutral-600 dark:text-neutral-400">
              {event.academy.sport} Academy
            </p>
          </div>
        </div>
        
        <div className="bg-gradient-to-r from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20 p-4 rounded-lg">
          <div className="flex items-center space-x-2 mb-2">
            <span className="text-lg">🎯</span>
            <span className="font-medium text-red-800 dark:text-red-200">
              {event.title}
            </span>
          </div>
          <div className="flex items-center space-x-4 text-xs text-neutral-600 dark:text-neutral-400 mb-2">
            <span>Type: {event.type}</span>
            <span>Location: {event.location}</span>
            {event._count?.participants > 0 && (
              <span>{event._count.participants} participants</span>
            )}
          </div>
        </div>
      </div>
    )
  }

  const renderMemberHighlightContent = () => {
    const member = item.contentData
    if (!member) return null

    return (
      <div className="space-y-3">
        <div className="flex items-center space-x-3">
          <div className="w-12 h-12 rounded-full bg-gradient-to-br from-pink-400 to-purple-500 flex items-center justify-center">
            {member.user.avatar ? (
              <img
                src={member.user.avatar}
                alt={`${member.user.firstName} ${member.user.lastName}`}
                className="w-full h-full rounded-full object-cover"
              />
            ) : (
              <span className="text-2xl">⭐</span>
            )}
          </div>
          <div>
            <h3 className="font-semibold text-neutral-900 dark:text-white">
              {member.user.firstName} {member.user.lastName}
            </h3>
            <p className="text-sm text-neutral-600 dark:text-neutral-400">
              {member.academy.name} • {member.academy.sport}
            </p>
          </div>
        </div>
        
        <div className="bg-gradient-to-r from-pink-50 to-purple-50 dark:from-pink-900/20 dark:to-purple-900/20 p-4 rounded-lg">
          <div className="flex items-center space-x-2 mb-2">
            <span className="text-lg">✨</span>
            <span className="font-medium text-pink-800 dark:text-pink-200">
              Member Highlight
            </span>
          </div>
          <p className="text-sm text-neutral-700 dark:text-neutral-300">
            Position: {member.position || 'Academy Member'}
          </p>
        </div>
      </div>
    )
  }

  const renderTrainingSessionContent = () => {
    const training = item.contentData
    if (!training) return null

    return (
      <div className="space-y-3">
        <div className="bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 p-4 rounded-lg">
          <div className="flex items-center space-x-2 mb-2">
            <span className="text-lg">🏃‍♂️</span>
            <span className="font-medium text-green-800 dark:text-green-200">
              Training Session
            </span>
          </div>
          <p className="text-sm text-neutral-700 dark:text-neutral-300">
            Type: {training.type} • Duration: {training.duration} minutes
          </p>
          {training._count?.attendances > 0 && (
            <p className="text-sm text-neutral-600 dark:text-neutral-400">
              {training._count.attendances} members participated
            </p>
          )}
        </div>
      </div>
    )
  }

  const renderContent = () => {
    switch (item.type) {
      case 'achievement':
        return renderAchievementContent()
      case 'academy_event':
        return renderEventContent()
      case 'member_highlight':
        return renderMemberHighlightContent()
      case 'training_session':
        return renderTrainingSessionContent()
      default:
        return null
    }
  }

  return (
    <div className="space-y-4">
      {renderContent()}
      
      {/* Academy-specific action buttons */}
      {item.type === 'academy_event' && item.contentData && (
        <div className="flex space-x-2">
          <Link
            href={`/academies/${item.contentData.academy.slug || item.contentData.academy.name.toLowerCase().replace(/\s+/g, '-')}`}
            className="text-xs bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400 px-3 py-1 rounded-full hover:bg-red-200 dark:hover:bg-red-900/30 transition-colors"
          >
            View Academy
          </Link>
        </div>
      )}
      
      {item.type === 'achievement' && item.contentData && (
        <div className="flex space-x-2">
          <Link
            href={`/academies/${item.contentData.academyMember.academy.slug || item.contentData.academyMember.academy.name.toLowerCase().replace(/\s+/g, '-')}`}
            className="text-xs bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400 px-3 py-1 rounded-full hover:bg-yellow-200 dark:hover:bg-yellow-900/30 transition-colors"
          >
            View Academy
          </Link>
        </div>
      )}
      
      {item.type === 'member_highlight' && item.contentData && (
        <div className="flex space-x-2">
          <Link
            href={`/academies/members/${item.contentData.id}`}
            className="text-xs bg-pink-100 text-pink-800 dark:bg-pink-900/20 dark:text-pink-400 px-3 py-1 rounded-full hover:bg-pink-200 dark:hover:bg-pink-900/30 transition-colors"
          >
            View Profile
          </Link>
          <Link
            href={`/academies/${item.contentData.academy.slug || item.contentData.academy.name.toLowerCase().replace(/\s+/g, '-')}`}
            className="text-xs bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400 px-3 py-1 rounded-full hover:bg-purple-200 dark:hover:bg-purple-900/30 transition-colors"
          >
            View Academy
          </Link>
        </div>
      )}
    </div>
  )
}
