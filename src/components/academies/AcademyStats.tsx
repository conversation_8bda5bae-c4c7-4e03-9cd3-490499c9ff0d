import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/Card'

interface AcademyStatsProps {
  academy: {
    id: number
    name: string
    sport: string
    _count?: {
      members: number
      trainings: number
      events: number
    }
  }
  content: any
}

export function AcademyStats({ academy, content }: AcademyStatsProps) {
  // Define brand accent colors for different sports
  const getSportAccentColor = (sport: string) => {
    const sportLower = sport.toLowerCase()

    if (sportLower.includes('football') || sportLower.includes('soccer')) {
      return 'text-green-600 dark:text-green-400'
    } else if (sportLower.includes('basketball')) {
      return 'text-orange-600 dark:text-orange-400'
    } else if (sportLower.includes('volleyball')) {
      return 'text-blue-600 dark:text-blue-400'
    } else if (sportLower.includes('athletics') || sportLower.includes('track')) {
      return 'text-purple-600 dark:text-purple-400'
    } else if (sportLower.includes('tennis')) {
      return 'text-yellow-600 dark:text-yellow-400'
    } else {
      return 'text-primary-600 dark:text-primary-400'
    }
  }

  const accentColor = getSportAccentColor(academy.sport)

  // Sample stats - in a real app, these would come from the database
  const stats = [
    {
      title: 'Success Rate',
      value: content?.successRate || '95%',
      description: 'Members achieving their goals',
      icon: '🎯'
    },
    {
      title: 'Professional Placements',
      value: content?.professionalPlacements || '24',
      description: 'Members signed to professional clubs',
      icon: '⭐'
    },
    {
      title: 'Years of Experience',
      value: content?.yearsOfExperience || '15+',
      description: 'Training young athletes',
      icon: '🏆'
    },
    {
      title: 'Qualified Coaches',
      value: content?.qualifiedCoaches || '8',
      description: 'Professional coaching staff',
      icon: '👨‍🏫'
    },
    {
      title: 'Training Hours',
      value: content?.trainingHours || '20+',
      description: 'Hours per week of training',
      icon: '⏱️'
    },
    {
      title: 'Facilities',
      value: content?.facilitiesCount || '5',
      description: 'Modern training facilities',
      icon: '🏟️'
    }
  ]

  return (
    <div className="bg-white dark:bg-neutral-800 py-12">
      <div className="container-mobile">
        <div className="mb-8 text-center">
          <h2 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100 md:text-3xl">
            Academy Excellence
          </h2>
          <p className="mt-2 text-neutral-600 dark:text-neutral-400">
            Why {academy.name} is the right choice for your athletic development
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {stats.map((stat, index) => (
            <Card key={index} variant="elevated" className="text-center">
              <CardHeader className="pb-2">
                <div className="text-3xl mb-2">{stat.icon}</div>
                <CardTitle className={`text-3xl font-bold ${accentColor}`}>
                  {stat.value}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <h3 className="font-semibold text-neutral-900 dark:text-neutral-100 mb-1">
                  {stat.title}
                </h3>
                <p className="text-sm text-neutral-600 dark:text-neutral-400">
                  {stat.description}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Additional Academy Highlights */}
        <div className="mt-12 grid gap-6 md:grid-cols-2">
          <Card variant="elevated">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <span>🏅</span>
                <span>Recent Achievements</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {content?.recentAchievements ? (
                content.recentAchievements.map((achievement: string, index: number) => (
                  <div key={index} className="flex items-start space-x-2">
                    <span className="text-primary-600 dark:text-primary-400">•</span>
                    <span className="text-sm text-neutral-600 dark:text-neutral-400">
                      {achievement}
                    </span>
                  </div>
                ))
              ) : (
                <>
                  <div className="flex items-start space-x-2">
                    <span className="text-primary-600 dark:text-primary-400">•</span>
                    <span className="text-sm text-neutral-600 dark:text-neutral-400">
                      Regional {academy.sport} championship winners 2024
                    </span>
                  </div>
                  <div className="flex items-start space-x-2">
                    <span className="text-primary-600 dark:text-primary-400">•</span>
                    <span className="text-sm text-neutral-600 dark:text-neutral-400">
                      5 members selected for national youth team
                    </span>
                  </div>
                  <div className="flex items-start space-x-2">
                    <span className="text-primary-600 dark:text-primary-400">•</span>
                    <span className="text-sm text-neutral-600 dark:text-neutral-400">
                      Best Academy Award - Tanzania Sports Federation
                    </span>
                  </div>
                </>
              )}
            </CardContent>
          </Card>

          <Card variant="elevated">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <span>🎓</span>
                <span>Training Programs</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {content?.programs ? (
                content.programs.map((program: any, index: number) => (
                  <div key={index} className="flex items-start space-x-2">
                    <span className="text-primary-600 dark:text-primary-400">•</span>
                    <div>
                      <span className="text-sm font-medium text-neutral-900 dark:text-neutral-100">
                        {program.name}
                      </span>
                      {program.description && (
                        <p className="text-xs text-neutral-600 dark:text-neutral-400">
                          {program.description}
                        </p>
                      )}
                    </div>
                  </div>
                ))
              ) : (
                <>
                  <div className="flex items-start space-x-2">
                    <span className="text-primary-600 dark:text-primary-400">•</span>
                    <div>
                      <span className="text-sm font-medium text-neutral-900 dark:text-neutral-100">
                        Youth Development Program
                      </span>
                      <p className="text-xs text-neutral-600 dark:text-neutral-400">
                        Ages 8-16, focus on fundamental skills
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-2">
                    <span className="text-primary-600 dark:text-primary-400">•</span>
                    <div>
                      <span className="text-sm font-medium text-neutral-900 dark:text-neutral-100">
                        Elite Performance Program
                      </span>
                      <p className="text-xs text-neutral-600 dark:text-neutral-400">
                        Ages 16+, advanced training for competitive play
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-2">
                    <span className="text-primary-600 dark:text-primary-400">•</span>
                    <div>
                      <span className="text-sm font-medium text-neutral-900 dark:text-neutral-100">
                        Professional Pathway
                      </span>
                      <p className="text-xs text-neutral-600 dark:text-neutral-400">
                        Direct connections to professional clubs
                      </p>
                    </div>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
