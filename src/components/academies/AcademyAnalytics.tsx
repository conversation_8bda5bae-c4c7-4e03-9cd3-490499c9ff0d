'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'

interface AnalyticsData {
  memberStats: {
    totalMembers: number
    activeMembers: number
    newMembersThisMonth: number
    membersBySkillLevel: Record<string, number>
    membersByPosition: Record<string, number>
  }
  performanceStats: {
    totalPerformanceRecords: number
    averageImprovement: number
    topPerformers: Array<{
      memberId: number
      memberName: string
      improvementScore: number
    }>
    skillDistribution: Record<string, number>
  }
  trainingStats: {
    totalTrainingSessions: number
    completedSessions: number
    averageAttendance: number
    trainingByType: Record<string, number>
    attendanceTrend: Array<{
      date: string
      attendance: number
    }>
  }
  eventStats: {
    totalEvents: number
    upcomingEvents: number
    participationRate: number
    eventsByType: Record<string, number>
  }
  partnershipStats: {
    totalPartnerships: number
    activePartnerships: number
    partnershipsByType: Record<string, number>
    revenueFromPartnerships: number
  }
}

interface AcademyAnalyticsProps {
  academyId: number
  academyName: string
  timeRange?: 'week' | 'month' | 'quarter' | 'year'
  className?: string
}

export function AcademyAnalytics({
  academyId,
  academyName,
  timeRange = 'month',
  className = ''
}: AcademyAnalyticsProps) {
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null)
  const [loading, setLoading] = useState(true)
  const [selectedTimeRange, setSelectedTimeRange] = useState(timeRange)

  useEffect(() => {
    fetchAnalytics()
  }, [academyId, selectedTimeRange])

  const fetchAnalytics = async () => {
    setLoading(true)
    try {
      // TODO: Replace with actual API call
      const response = await fetch(`/api/academies/${academyId}/analytics?timeRange=${selectedTimeRange}`)
      if (response.ok) {
        const data = await response.json()
        setAnalytics(data)
      } else {
        // Mock data for development
        setAnalytics({
          memberStats: {
            totalMembers: 45,
            activeMembers: 38,
            newMembersThisMonth: 7,
            membersBySkillLevel: {
              'beginner': 15,
              'intermediate': 20,
              'advanced': 10
            },
            membersByPosition: {
              'forward': 12,
              'midfielder': 15,
              'defender': 10,
              'goalkeeper': 8
            }
          },
          performanceStats: {
            totalPerformanceRecords: 234,
            averageImprovement: 15.3,
            topPerformers: [
              { memberId: 1, memberName: 'John Doe', improvementScore: 25.5 },
              { memberId: 2, memberName: 'Jane Smith', improvementScore: 22.1 },
              { memberId: 3, memberName: 'Mike Johnson', improvementScore: 19.8 }
            ],
            skillDistribution: {
              'speed': 85,
              'agility': 78,
              'strength': 72,
              'endurance': 88
            }
          },
          trainingStats: {
            totalTrainingSessions: 24,
            completedSessions: 22,
            averageAttendance: 82.5,
            trainingByType: {
              'technical': 8,
              'physical': 6,
              'tactical': 5,
              'mental': 3
            },
            attendanceTrend: [
              { date: '2024-01-01', attendance: 85 },
              { date: '2024-01-08', attendance: 78 },
              { date: '2024-01-15', attendance: 82 },
              { date: '2024-01-22', attendance: 88 }
            ]
          },
          eventStats: {
            totalEvents: 8,
            upcomingEvents: 3,
            participationRate: 75.2,
            eventsByType: {
              'competition': 3,
              'friendly': 2,
              'tournament': 2,
              'showcase': 1
            }
          },
          partnershipStats: {
            totalPartnerships: 5,
            activePartnerships: 4,
            partnershipsByType: {
              'scouting': 2,
              'sponsorship': 2,
              'training': 1
            },
            revenueFromPartnerships: 25000
          }
        })
      }
    } catch (error) {
      console.error('Error fetching analytics:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`
  }

  if (loading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100">
            Academy Analytics
          </h2>
          <div className="animate-pulse bg-neutral-200 dark:bg-neutral-700 h-10 w-32 rounded-lg" />
        </div>
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          {[...Array(8)].map((_, i) => (
            <div key={i} className="animate-pulse">
              <div className="bg-neutral-200 dark:bg-neutral-700 h-32 rounded-lg" />
            </div>
          ))}
        </div>
      </div>
    )
  }

  if (!analytics) {
    return (
      <div className={`text-center py-12 ${className}`}>
        <p className="text-neutral-600 dark:text-neutral-400">
          Unable to load analytics data. Please try again later.
        </p>
        <Button onClick={fetchAnalytics} className="mt-4">
          Retry
        </Button>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100">
          {academyName} Analytics
        </h2>
        <div className="flex items-center space-x-2">
          <select
            value={selectedTimeRange}
            onChange={(e) => setSelectedTimeRange(e.target.value as any)}
            className="px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100"
          >
            <option value="week">Last Week</option>
            <option value="month">Last Month</option>
            <option value="quarter">Last Quarter</option>
            <option value="year">Last Year</option>
          </select>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-neutral-600 dark:text-neutral-400">
              Total Members
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-neutral-900 dark:text-neutral-100">
              {analytics.memberStats.totalMembers}
            </div>
            <p className="text-xs text-green-600 dark:text-green-400">
              +{analytics.memberStats.newMembersThisMonth} this month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-neutral-600 dark:text-neutral-400">
              Active Members
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-neutral-900 dark:text-neutral-100">
              {analytics.memberStats.activeMembers}
            </div>
            <p className="text-xs text-neutral-600 dark:text-neutral-400">
              {formatPercentage((analytics.memberStats.activeMembers / analytics.memberStats.totalMembers) * 100)} of total
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-neutral-600 dark:text-neutral-400">
              Training Attendance
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-neutral-900 dark:text-neutral-100">
              {formatPercentage(analytics.trainingStats.averageAttendance)}
            </div>
            <p className="text-xs text-neutral-600 dark:text-neutral-400">
              {analytics.trainingStats.completedSessions}/{analytics.trainingStats.totalTrainingSessions} sessions
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-neutral-600 dark:text-neutral-400">
              Partnership Revenue
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-neutral-900 dark:text-neutral-100">
              {formatCurrency(analytics.partnershipStats.revenueFromPartnerships)}
            </div>
            <p className="text-xs text-neutral-600 dark:text-neutral-400">
              {analytics.partnershipStats.activePartnerships} active partnerships
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Member Distribution */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Members by Skill Level</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {Object.entries(analytics.memberStats.membersBySkillLevel).map(([level, count]) => (
                <div key={level} className="flex items-center justify-between">
                  <span className="text-sm font-medium capitalize">{level}</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-24 bg-neutral-200 dark:bg-neutral-700 rounded-full h-2">
                      <div
                        className="bg-primary-600 h-2 rounded-full"
                        style={{
                          width: `${(count / analytics.memberStats.totalMembers) * 100}%`
                        }}
                      />
                    </div>
                    <span className="text-sm text-neutral-600 dark:text-neutral-400 w-8">
                      {count}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Training Types</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {Object.entries(analytics.trainingStats.trainingByType).map(([type, count]) => (
                <div key={type} className="flex items-center justify-between">
                  <span className="text-sm font-medium capitalize">{type}</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-24 bg-neutral-200 dark:bg-neutral-700 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full"
                        style={{
                          width: `${(count / analytics.trainingStats.totalTrainingSessions) * 100}%`
                        }}
                      />
                    </div>
                    <span className="text-sm text-neutral-600 dark:text-neutral-400 w-8">
                      {count}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
