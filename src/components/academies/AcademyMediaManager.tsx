'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/Button'
import { AcademyMediaUpload, AcademyMediaFile } from './AcademyMediaUpload'
import { AcademyMediaGallery } from './AcademyMediaGallery'
import { getAcademyMedia, uploadMemberTalentMedia, addTrainingMedia, addEventMedia, deleteAcademyMedia } from '@/app/actions/academies'

interface AcademyMediaManagerProps {
  academyId: number
  academyName: string
  canManage?: boolean
  memberId?: number // For member-specific media
  trainingId?: number // For training-specific media
  eventId?: number // For event-specific media
  className?: string
}

export function AcademyMediaManager({
  academyId,
  academyName,
  canManage = false,
  memberId,
  trainingId,
  eventId,
  className = ''
}: AcademyMediaManagerProps) {
  const [media, setMedia] = useState<AcademyMediaFile[]>([])
  const [loading, setLoading] = useState(true)
  const [showUpload, setShowUpload] = useState(false)
  const [uploadCategory, setUploadCategory] = useState<'talent' | 'achievement' | 'training' | 'event' | 'certificate' | 'general'>('general')
  const [error, setError] = useState<string | null>(null)

  // Determine context and available categories
  const getAvailableCategories = () => {
    if (memberId) {
      return ['talent', 'achievement', 'certificate'] as const
    }
    if (trainingId) {
      return ['training'] as const
    }
    if (eventId) {
      return ['event'] as const
    }
    return ['talent', 'achievement', 'training', 'event', 'certificate', 'general'] as const
  }

  const availableCategories = getAvailableCategories()

  // Load media on component mount
  useEffect(() => {
    loadMedia()
  }, [academyId])

  const loadMedia = async () => {
    try {
      setLoading(true)
      setError(null)
      
      const result = await getAcademyMedia(academyId)
      
      if (result.success) {
        setMedia(result.media || [])
      } else {
        setError(result.error || 'Failed to load media')
      }
    } catch (err) {
      setError('Failed to load media')
      console.error('Error loading media:', err)
    } finally {
      setLoading(false)
    }
  }

  const handleUpload = async (files: AcademyMediaFile[]) => {
    try {
      setError(null)
      
      // Convert AcademyMediaFile to the format expected by server actions
      const mediaFiles = files.map(file => ({
        url: file.url,
        type: file.type,
        title: file.title,
        description: file.description,
        tags: file.tags
      }))

      let result

      // Upload based on context
      if (memberId && uploadCategory === 'talent') {
        result = await uploadMemberTalentMedia(memberId, mediaFiles)
      } else if (trainingId && uploadCategory === 'training') {
        result = await addTrainingMedia(trainingId, mediaFiles)
      } else if (eventId && uploadCategory === 'event') {
        result = await addEventMedia(eventId, mediaFiles)
      } else {
        // For other categories, we'll need to implement specific handlers
        // For now, just show success
        result = { success: true }
      }

      if (result.success) {
        setShowUpload(false)
        await loadMedia() // Reload media
      } else {
        setError(result.error || 'Upload failed')
      }
    } catch (err) {
      setError('Upload failed')
      console.error('Error uploading media:', err)
    }
  }

  const handleDelete = async (mediaId: string) => {
    try {
      setError(null)
      
      // Determine context ID for deletion
      let contextId = academyId
      let category: 'talent' | 'achievement' | 'training' | 'event' = 'training'
      
      if (memberId) {
        contextId = memberId
        category = 'talent'
      } else if (trainingId) {
        contextId = trainingId
        category = 'training'
      } else if (eventId) {
        contextId = eventId
        category = 'event'
      }

      const result = await deleteAcademyMedia(mediaId, category, contextId)
      
      if (result.success) {
        await loadMedia() // Reload media
      } else {
        setError(result.error || 'Delete failed')
      }
    } catch (err) {
      setError('Delete failed')
      console.error('Error deleting media:', err)
    }
  }

  const getContextTitle = () => {
    if (memberId) return 'Member Media'
    if (trainingId) return 'Training Media'
    if (eventId) return 'Event Media'
    return `${academyName} Media`
  }

  const getContextDescription = () => {
    if (memberId) return 'Manage talent videos, achievements, and certificates for this member'
    if (trainingId) return 'Manage media for this training session'
    if (eventId) return 'Manage media for this event'
    return 'Manage all media for this academy'
  }

  if (loading) {
    return (
      <div className={`flex items-center justify-center py-12 ${className}`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto mb-4"></div>
          <p className="text-neutral-600 dark:text-neutral-400">Loading media...</p>
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100">
            {getContextTitle()}
          </h2>
          <p className="text-neutral-600 dark:text-neutral-400">
            {getContextDescription()}
          </p>
        </div>
        
        {canManage && (
          <Button onClick={() => setShowUpload(true)}>
            Upload Media
          </Button>
        )}
      </div>

      {/* Error Display */}
      {error && (
        <div className="p-4 rounded-lg bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800">
          <p className="text-red-800 dark:text-red-200">{error}</p>
        </div>
      )}

      {/* Upload Modal */}
      {showUpload && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
          <div className="bg-white dark:bg-neutral-900 rounded-lg p-6 max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-neutral-900 dark:text-neutral-100">
                Upload Media
              </h3>
              <button
                onClick={() => setShowUpload(false)}
                className="text-neutral-500 hover:text-neutral-700 dark:hover:text-neutral-300"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {/* Category Selection */}
            {availableCategories.length > 1 && (
              <div className="mb-6">
                <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2">
                  Media Category
                </label>
                <select
                  value={uploadCategory}
                  onChange={(e) => setUploadCategory(e.target.value as any)}
                  className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100"
                >
                  {availableCategories.map(category => (
                    <option key={category} value={category}>
                      {category.charAt(0).toUpperCase() + category.slice(1)}
                    </option>
                  ))}
                </select>
              </div>
            )}

            <AcademyMediaUpload
              onUpload={handleUpload}
              category={uploadCategory}
              academyId={academyId}
              memberId={memberId}
              maxFiles={uploadCategory === 'event' ? 15 : uploadCategory === 'talent' ? 10 : 5}
            />
          </div>
        </div>
      )}

      {/* Media Gallery */}
      <AcademyMediaGallery
        media={media}
        showFilters={!memberId && !trainingId && !eventId} // Only show filters for academy-wide view
        showUploadButton={false} // We have our own upload button
        canManage={canManage}
        onDelete={canManage ? handleDelete : undefined}
      />
    </div>
  )
}
