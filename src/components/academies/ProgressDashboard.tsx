'use client'

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/Card'

interface ProgressDashboardProps {
  member: {
    id: number
    joinedAt: Date
    performances: Array<{
      id: number
      metricType: string
      value: number
      unit: string
      recordedAt: Date
    }>
    achievements: Array<{
      id: number
      type: string
      level: string
      achievedAt: Date
    }>
    trainings?: Array<{
      id: number
      attendanceStatus: string
      attendedAt: Date
    }>
  }
}

export function ProgressDashboard({ member }: ProgressDashboardProps) {
  const calculateMembershipDuration = () => {
    const now = new Date()
    const joined = new Date(member.joinedAt)
    const diffTime = Math.abs(now.getTime() - joined.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    
    if (diffDays < 30) {
      return `${diffDays} days`
    } else if (diffDays < 365) {
      const months = Math.floor(diffDays / 30)
      return `${months} month${months !== 1 ? 's' : ''}`
    } else {
      const years = Math.floor(diffDays / 365)
      const remainingMonths = Math.floor((diffDays % 365) / 30)
      return `${years} year${years !== 1 ? 's' : ''}${remainingMonths > 0 ? ` ${remainingMonths} month${remainingMonths !== 1 ? 's' : ''}` : ''}`
    }
  }

  const getRecentActivity = () => {
    const activities: Array<{
      type: string
      title: string
      subtitle?: string
      date: Date
      icon: string
    }> = []
    
    // Add recent performances
    member.performances.slice(0, 3).forEach(performance => {
      activities.push({
        type: 'performance',
        title: `Recorded ${performance.metricType}`,
        subtitle: `${performance.value} ${performance.unit}`,
        date: new Date(performance.recordedAt),
        icon: '📊'
      })
    })

    // Add recent achievements
    member.achievements.slice(0, 3).forEach(achievement => {
      activities.push({
        type: 'achievement',
        title: achievement.type,
        subtitle: `${achievement.level} level`,
        date: new Date(achievement.achievedAt),
        icon: '🏆'
      })
    })

    // Sort by date and return top 5
    return activities
      .sort((a, b) => b.date.getTime() - a.date.getTime())
      .slice(0, 5)
  }

  const getPerformanceTrends = () => {
    const trends: Record<string, Array<{ value: number; date: Date }>> = {}
    
    member.performances.forEach(performance => {
      if (!trends[performance.metricType]) {
        trends[performance.metricType] = []
      }
      trends[performance.metricType].push({
        value: performance.value,
        date: new Date(performance.recordedAt)
      })
    })

    // Calculate trends for each metric
    const trendSummary = Object.entries(trends).map(([metricType, values]) => {
      const sortedValues = values.sort((a, b) => a.date.getTime() - b.date.getTime())
      
      if (sortedValues.length < 2) {
        return { metricType, trend: 'stable', change: 0, percentage: 0 }
      }

      const latest = sortedValues[sortedValues.length - 1]
      const previous = sortedValues[sortedValues.length - 2]
      const change = latest.value - previous.value
      
      // For time-based metrics, improvement is negative change
      const isTimeMetric = metricType.toLowerCase().includes('time') || metricType.toLowerCase().includes('speed')
      const isImproving = isTimeMetric ? change < 0 : change > 0
      
      return {
        metricType,
        trend: isImproving ? 'improving' : change === 0 ? 'stable' : 'declining',
        change: Math.abs(change),
        percentage: Math.abs((change / previous.value) * 100)
      }
    })

    return trendSummary
  }

  const getAchievementBreakdown = () => {
    const breakdown: Record<string, number> = {}
    
    member.achievements.forEach(achievement => {
      if (!breakdown[achievement.level]) {
        breakdown[achievement.level] = 0
      }
      breakdown[achievement.level]++
    })

    return breakdown
  }

  const getTrainingAttendance = () => {
    if (!member.trainings) return null
    
    const total = member.trainings.length
    const attended = member.trainings.filter(t => t.attendanceStatus === 'present').length
    const attendanceRate = total > 0 ? (attended / total) * 100 : 0
    
    return {
      total,
      attended,
      attendanceRate
    }
  }

  const recentActivity = getRecentActivity()
  const performanceTrends = getPerformanceTrends()
  const achievementBreakdown = getAchievementBreakdown()
  const trainingAttendance = getTrainingAttendance()

  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
      {/* Membership Overview */}
      <Card>
        <CardHeader>
          <CardTitle>Membership Overview</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <div className="text-2xl font-bold text-primary-600">
                {calculateMembershipDuration()}
              </div>
              <div className="text-sm text-neutral-600 dark:text-neutral-400">
                Member since {new Date(member.joinedAt).toLocaleDateString()}
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4 text-center">
              <div>
                <div className="text-lg font-semibold text-neutral-900 dark:text-neutral-100">
                  {member.performances.length}
                </div>
                <div className="text-xs text-neutral-600 dark:text-neutral-400">
                  Performance Records
                </div>
              </div>
              <div>
                <div className="text-lg font-semibold text-neutral-900 dark:text-neutral-100">
                  {member.achievements.length}
                </div>
                <div className="text-xs text-neutral-600 dark:text-neutral-400">
                  Achievements
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Performance Trends */}
      <Card>
        <CardHeader>
          <CardTitle>Performance Trends</CardTitle>
        </CardHeader>
        <CardContent>
          {performanceTrends.length > 0 ? (
            <div className="space-y-3">
              {performanceTrends.slice(0, 4).map((trend, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="text-sm font-medium text-neutral-900 dark:text-neutral-100">
                    {trend.metricType}
                  </div>
                  <div className={`flex items-center space-x-1 text-sm ${
                    trend.trend === 'improving' 
                      ? 'text-green-600 dark:text-green-400'
                      : trend.trend === 'declining'
                      ? 'text-red-600 dark:text-red-400'
                      : 'text-neutral-600 dark:text-neutral-400'
                  }`}>
                    <span>
                      {trend.trend === 'improving' ? '↗️' : trend.trend === 'declining' ? '↘️' : '➡️'}
                    </span>
                    <span>
                      {trend.trend === 'stable' ? 'Stable' : `${trend.percentage.toFixed(1)}%`}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-4">
              <div className="text-neutral-400 mb-2">📈</div>
              <p className="text-sm text-neutral-600 dark:text-neutral-400">
                No performance trends yet
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Achievement Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle>Achievement Levels</CardTitle>
        </CardHeader>
        <CardContent>
          {Object.keys(achievementBreakdown).length > 0 ? (
            <div className="space-y-3">
              {Object.entries(achievementBreakdown).map(([level, count]) => (
                <div key={level} className="flex items-center justify-between">
                  <div className="text-sm font-medium text-neutral-900 dark:text-neutral-100">
                    {level}
                  </div>
                  <div className="text-sm text-primary-600 font-semibold">
                    {count}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-4">
              <div className="text-neutral-400 mb-2">🏆</div>
              <p className="text-sm text-neutral-600 dark:text-neutral-400">
                No achievements yet
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Training Attendance */}
      {trainingAttendance && (
        <Card>
          <CardHeader>
            <CardTitle>Training Attendance</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <div className="text-2xl font-bold text-primary-600">
                  {trainingAttendance.attendanceRate.toFixed(1)}%
                </div>
                <div className="text-sm text-neutral-600 dark:text-neutral-400">
                  Attendance Rate
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4 text-center">
                <div>
                  <div className="text-lg font-semibold text-green-600">
                    {trainingAttendance.attended}
                  </div>
                  <div className="text-xs text-neutral-600 dark:text-neutral-400">
                    Attended
                  </div>
                </div>
                <div>
                  <div className="text-lg font-semibold text-neutral-900 dark:text-neutral-100">
                    {trainingAttendance.total}
                  </div>
                  <div className="text-xs text-neutral-600 dark:text-neutral-400">
                    Total Sessions
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Recent Activity */}
      <Card className="md:col-span-2 lg:col-span-2">
        <CardHeader>
          <CardTitle>Recent Activity</CardTitle>
        </CardHeader>
        <CardContent>
          {recentActivity.length > 0 ? (
            <div className="space-y-3">
              {recentActivity.map((activity, index) => (
                <div key={index} className="flex items-center space-x-3 p-3 border border-neutral-200 dark:border-neutral-700 rounded-lg">
                  <div className="text-xl">{activity.icon}</div>
                  <div className="flex-1">
                    <div className="font-medium text-neutral-900 dark:text-neutral-100">
                      {activity.title}
                    </div>
                    <div className="text-sm text-neutral-600 dark:text-neutral-400">
                      {activity.subtitle}
                    </div>
                  </div>
                  <div className="text-sm text-neutral-500">
                    {activity.date.toLocaleDateString()}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <div className="text-neutral-400 mb-2">📅</div>
              <p className="text-neutral-600 dark:text-neutral-400">
                No recent activity
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
