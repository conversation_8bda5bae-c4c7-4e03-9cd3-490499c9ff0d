'use client'

import { useState, useRef, useCallback } from 'react'
import { But<PERSON> } from '@/components/ui/Button'
import { CloudinaryUpload } from '@/components/ui/CloudinaryUpload'

export interface AcademyMediaFile {
  url: string
  publicId?: string
  type: 'image' | 'video'
  fileName: string
  size: number
  width?: number
  height?: number
  category: 'talent' | 'achievement' | 'training' | 'event' | 'certificate' | 'general'
  title?: string
  description?: string
  tags?: string[]
}

interface AcademyMediaUploadProps {
  onUpload: (files: AcademyMediaFile[]) => void
  category: 'talent' | 'achievement' | 'training' | 'event' | 'certificate' | 'general'
  maxFiles?: number
  allowImages?: boolean
  allowVideos?: boolean
  className?: string
  academyId?: number
  memberId?: number
}

export function AcademyMediaUpload({
  onUpload,
  category,
  maxFiles = 5,
  allowImages = true,
  allowVideos = true,
  className = '',
  academyId,
  memberId
}: AcademyMediaUploadProps) {
  const [uploading, setUploading] = useState(false)
  const [uploadedFiles, setUploadedFiles] = useState<AcademyMediaFile[]>([])
  const [showMetadataForm, setShowMetadataForm] = useState(false)
  const [currentFileIndex, setCurrentFileIndex] = useState(0)
  const [metadata, setMetadata] = useState<{
    title: string
    description: string
    tags: string[]
  }>({
    title: '',
    description: '',
    tags: []
  })

  // Determine folder based on category and IDs
  const getUploadFolder = () => {
    const baseFolder = 'glbiashara/academies'
    if (academyId) {
      if (memberId) {
        return `${baseFolder}/academy-${academyId}/members/member-${memberId}/${category}`
      }
      return `${baseFolder}/academy-${academyId}/${category}`
    }
    return `${baseFolder}/${category}`
  }

  const handleCloudinaryUpload = useCallback(async (files: any[]) => {
    setUploading(true)
    try {
      const academyFiles: AcademyMediaFile[] = files.map(file => ({
        ...file,
        category,
        title: '',
        description: '',
        tags: []
      }))

      setUploadedFiles(academyFiles)
      
      // If we have files and need metadata, show form
      if (academyFiles.length > 0 && (category === 'talent' || category === 'achievement')) {
        setCurrentFileIndex(0)
        setShowMetadataForm(true)
      } else {
        // For simple uploads, call onUpload immediately
        onUpload(academyFiles)
      }
    } catch (error) {
      console.error('Upload error:', error)
    } finally {
      setUploading(false)
    }
  }, [category, onUpload])

  const handleMetadataSubmit = () => {
    if (currentFileIndex < uploadedFiles.length) {
      // Update current file with metadata
      const updatedFiles = [...uploadedFiles]
      updatedFiles[currentFileIndex] = {
        ...updatedFiles[currentFileIndex],
        title: metadata.title,
        description: metadata.description,
        tags: metadata.tags
      }
      setUploadedFiles(updatedFiles)

      // Move to next file or finish
      if (currentFileIndex < uploadedFiles.length - 1) {
        setCurrentFileIndex(currentFileIndex + 1)
        setMetadata({ title: '', description: '', tags: [] })
      } else {
        // All files processed
        setShowMetadataForm(false)
        onUpload(updatedFiles)
      }
    }
  }

  const addTag = (tag: string) => {
    if (tag.trim() && !metadata.tags.includes(tag.trim())) {
      setMetadata(prev => ({
        ...prev,
        tags: [...prev.tags, tag.trim()]
      }))
    }
  }

  const removeTag = (tagToRemove: string) => {
    setMetadata(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }))
  }

  const getCategoryConfig = () => {
    switch (category) {
      case 'talent':
        return {
          title: 'Talent Videos & Images',
          description: 'Upload videos and images showcasing athletic talent and skills',
          allowVideos: true,
          allowImages: true,
          maxFiles: 10
        }
      case 'achievement':
        return {
          title: 'Achievement Media',
          description: 'Upload certificates, awards, and achievement photos',
          allowVideos: false,
          allowImages: true,
          maxFiles: 5
        }
      case 'training':
        return {
          title: 'Training Session Media',
          description: 'Upload training videos and session photos',
          allowVideos: true,
          allowImages: true,
          maxFiles: 8
        }
      case 'event':
        return {
          title: 'Event Media',
          description: 'Upload event photos and videos',
          allowVideos: true,
          allowImages: true,
          maxFiles: 15
        }
      case 'certificate':
        return {
          title: 'Certificates & Documents',
          description: 'Upload official certificates and documents',
          allowVideos: false,
          allowImages: true,
          maxFiles: 3
        }
      default:
        return {
          title: 'Media Upload',
          description: 'Upload images and videos',
          allowVideos: true,
          allowImages: true,
          maxFiles: 5
        }
    }
  }

  const config = getCategoryConfig()

  if (showMetadataForm && uploadedFiles.length > 0) {
    const currentFile = uploadedFiles[currentFileIndex]
    
    return (
      <div className="space-y-4">
        <div className="text-center">
          <h3 className="text-lg font-semibold text-neutral-900 dark:text-neutral-100">
            Add Details for {currentFile.fileName}
          </h3>
          <p className="text-sm text-neutral-600 dark:text-neutral-400">
            File {currentFileIndex + 1} of {uploadedFiles.length}
          </p>
        </div>

        {/* File Preview */}
        <div className="flex justify-center">
          <div className="w-32 h-32 rounded-lg overflow-hidden bg-neutral-200 dark:bg-neutral-700">
            {currentFile.type === 'video' ? (
              <video
                src={currentFile.url}
                className="w-full h-full object-cover"
                preload="metadata"
              />
            ) : (
              <img
                src={currentFile.url}
                alt="Preview"
                className="w-full h-full object-cover"
              />
            )}
          </div>
        </div>

        {/* Metadata Form */}
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1">
              Title
            </label>
            <input
              type="text"
              value={metadata.title}
              onChange={(e) => setMetadata(prev => ({ ...prev, title: e.target.value }))}
              className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100"
              placeholder="Enter a title for this media"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1">
              Description
            </label>
            <textarea
              value={metadata.description}
              onChange={(e) => setMetadata(prev => ({ ...prev, description: e.target.value }))}
              rows={3}
              className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100"
              placeholder="Describe this media"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1">
              Tags
            </label>
            <div className="flex flex-wrap gap-2 mb-2">
              {metadata.tags.map((tag, index) => (
                <span
                  key={index}
                  className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200"
                >
                  {tag}
                  <button
                    type="button"
                    onClick={() => removeTag(tag)}
                    className="ml-1 text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-200"
                  >
                    ×
                  </button>
                </span>
              ))}
            </div>
            <input
              type="text"
              onKeyPress={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault()
                  addTag(e.currentTarget.value)
                  e.currentTarget.value = ''
                }
              }}
              className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100"
              placeholder="Add tags (press Enter to add)"
            />
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-between">
          <Button
            variant="outline"
            onClick={() => {
              setShowMetadataForm(false)
              setUploadedFiles([])
            }}
          >
            Cancel
          </Button>
          <Button onClick={handleMetadataSubmit}>
            {currentFileIndex < uploadedFiles.length - 1 ? 'Next File' : 'Complete Upload'}
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-4 ${className}`}>
      <div>
        <h3 className="text-lg font-semibold text-neutral-900 dark:text-neutral-100 mb-1">
          {config.title}
        </h3>
        <p className="text-sm text-neutral-600 dark:text-neutral-400">
          {config.description}
        </p>
      </div>

      <CloudinaryUpload
        onUpload={handleCloudinaryUpload}
        maxFiles={maxFiles || config.maxFiles}
        allowImages={allowImages && config.allowImages}
        allowVideos={allowVideos && config.allowVideos}
        folder={getUploadFolder()}
      />

      {uploading && (
        <div className="text-center py-4">
          <div className="inline-flex items-center px-4 py-2 rounded-lg bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-600 mr-2"></div>
            Processing upload...
          </div>
        </div>
      )}
    </div>
  )
}
