'use client'

import { useState } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { recordPerformance } from '@/app/actions/academies'

interface Performance {
  id: number
  metricType: string
  value: number
  unit: string
  recordedAt: Date
  details?: any
  training?: {
    title: string
    type: string
    scheduledAt: Date
  }
}

interface PerformanceTrackerProps {
  memberId: number
  performances: Performance[]
  canRecord?: boolean
}

export function PerformanceTracker({ memberId, performances, canRecord = false }: PerformanceTrackerProps) {
  const [showAddForm, setShowAddForm] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [formData, setFormData] = useState({
    metricType: '',
    value: '',
    unit: '',
    details: {
      notes: '',
      conditions: '',
      improvement: ''
    }
  })

  const metricTypes = [
    { type: 'Speed', unit: 'seconds', description: '40-yard dash time' },
    { type: 'Endurance', unit: 'minutes', description: 'Cooper test (12-minute run)' },
    { type: 'Agility', unit: 'seconds', description: 'Cone drill time' },
    { type: 'Strength', unit: 'kg', description: 'Maximum weight lifted' },
    { type: 'Accuracy', unit: '%', description: 'Shot accuracy percentage' },
    { type: 'Ball Control', unit: 'score', description: 'Technical skill rating (1-10)' },
    { type: 'Passing', unit: '%', description: 'Pass completion rate' },
    { type: 'Dribbling', unit: 'score', description: 'Dribbling skill rating (1-10)' },
    { type: 'Jumping', unit: 'cm', description: 'Vertical jump height' },
    { type: 'Flexibility', unit: 'cm', description: 'Sit and reach test' }
  ]

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      const result = await recordPerformance({
        academyMemberId: memberId,
        metricType: formData.metricType,
        value: parseFloat(formData.value),
        unit: formData.unit,
        details: formData.details,
        recordedBy: 'Academy Coach' // Default value, could be made dynamic
      })

      if (result.success) {
        setShowAddForm(false)
        setFormData({
          metricType: '',
          value: '',
          unit: '',
          details: { notes: '', conditions: '', improvement: '' }
        })
        // Refresh the page or update the performances list
        window.location.reload()
      } else {
        alert(result.error || 'Failed to record performance')
      }
    } catch (error) {
      console.error('Error recording performance:', error)
      alert('Failed to record performance')
    } finally {
      setIsLoading(false)
    }
  }

  const getMetricIcon = (metricType: string) => {
    switch (metricType.toLowerCase()) {
      case 'speed': return '⚡'
      case 'endurance': return '🏃'
      case 'agility': return '🤸'
      case 'strength': return '💪'
      case 'accuracy': return '🎯'
      case 'ball control': return '⚽'
      case 'passing': return '🎯'
      case 'dribbling': return '🏃‍♂️'
      case 'jumping': return '🦘'
      case 'flexibility': return '🧘'
      default: return '📊'
    }
  }

  const getPerformanceTrend = (metricType: string) => {
    const metricPerformances = performances
      .filter(p => p.metricType === metricType)
      .sort((a, b) => new Date(a.recordedAt).getTime() - new Date(b.recordedAt).getTime())

    if (metricPerformances.length < 2) return null

    const latest = metricPerformances[metricPerformances.length - 1]
    const previous = metricPerformances[metricPerformances.length - 2]

    const improvement = latest.value - previous.value
    const isImprovement = metricType.toLowerCase().includes('time') || metricType.toLowerCase().includes('speed') 
      ? improvement < 0 // For time-based metrics, lower is better
      : improvement > 0 // For other metrics, higher is better

    return {
      improvement,
      isImprovement,
      percentage: Math.abs((improvement / previous.value) * 100)
    }
  }

  const groupedPerformances = performances.reduce((acc, performance) => {
    if (!acc[performance.metricType]) {
      acc[performance.metricType] = []
    }
    acc[performance.metricType].push(performance)
    return acc
  }, {} as Record<string, Performance[]>)

  return (
    <section className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100">
            Performance Tracking
          </h2>
          <p className="text-neutral-600 dark:text-neutral-400">
            Monitor progress and improvement over time
          </p>
        </div>
        {canRecord && (
          <Button onClick={() => setShowAddForm(true)}>
            Record Performance
          </Button>
        )}
      </div>

      {Object.keys(groupedPerformances).length > 0 ? (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {Object.entries(groupedPerformances).map(([metricType, metricPerformances]) => {
            const latest = metricPerformances.sort((a, b) => 
              new Date(b.recordedAt).getTime() - new Date(a.recordedAt).getTime()
            )[0]
            const trend = getPerformanceTrend(metricType)

            return (
              <Card key={metricType} variant="elevated">
                <CardHeader>
                  <div className="flex items-center space-x-3">
                    <div className="text-2xl">
                      {getMetricIcon(metricType)}
                    </div>
                    <div>
                      <CardTitle className="text-lg">{metricType}</CardTitle>
                      <p className="text-sm text-neutral-600 dark:text-neutral-400">
                        {metricPerformances.length} record{metricPerformances.length !== 1 ? 's' : ''}
                      </p>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <div className="text-2xl font-bold text-primary-600">
                        {latest.value} {latest.unit}
                      </div>
                      <div className="text-sm text-neutral-600 dark:text-neutral-400">
                        Latest: {new Date(latest.recordedAt).toLocaleDateString()}
                      </div>
                    </div>

                    {trend && (
                      <div className={`flex items-center space-x-2 text-sm ${
                        trend.isImprovement 
                          ? 'text-green-600 dark:text-green-400' 
                          : 'text-red-600 dark:text-red-400'
                      }`}>
                        <span>{trend.isImprovement ? '↗️' : '↘️'}</span>
                        <span>
                          {trend.percentage.toFixed(1)}% {trend.isImprovement ? 'improvement' : 'decline'}
                        </span>
                      </div>
                    )}

                    {latest.details?.notes && (
                      <div>
                        <h4 className="text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1">
                          Notes
                        </h4>
                        <p className="text-sm text-neutral-600 dark:text-neutral-400">
                          {latest.details.notes}
                        </p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>
      ) : (
        <Card>
          <CardContent className="text-center py-12">
            <div className="text-4xl mb-4">📊</div>
            <h3 className="text-lg font-medium text-neutral-900 dark:text-neutral-100 mb-2">
              No Performance Records
            </h3>
            <p className="text-neutral-600 dark:text-neutral-400 mb-6">
              Start tracking performance metrics to monitor progress and improvement.
            </p>
            {canRecord && (
              <Button onClick={() => setShowAddForm(true)}>
                Record First Performance
              </Button>
            )}
          </CardContent>
        </Card>
      )}

      {/* Add Performance Modal */}
      {showAddForm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <CardHeader>
              <CardTitle>Record Performance</CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2">
                      Metric Type
                    </label>
                    <select
                      value={formData.metricType}
                      onChange={(e) => {
                        const selectedMetric = metricTypes.find(m => m.type === e.target.value)
                        setFormData({
                          ...formData,
                          metricType: e.target.value,
                          unit: selectedMetric?.unit || ''
                        })
                      }}
                      className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100"
                      required
                    >
                      <option value="">Select metric...</option>
                      {metricTypes.map((metric) => (
                        <option key={metric.type} value={metric.type}>
                          {metric.type} ({metric.description})
                        </option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2">
                      Value
                    </label>
                    <div className="flex space-x-2">
                      <input
                        type="number"
                        step="0.01"
                        value={formData.value}
                        onChange={(e) => setFormData({ ...formData, value: e.target.value })}
                        className="flex-1 px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100"
                        placeholder="0.00"
                        required
                      />
                      <input
                        type="text"
                        value={formData.unit}
                        onChange={(e) => setFormData({ ...formData, unit: e.target.value })}
                        className="w-20 px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100"
                        placeholder="unit"
                        required
                      />
                    </div>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2">
                    Notes
                  </label>
                  <textarea
                    value={formData.details.notes}
                    onChange={(e) => setFormData({
                      ...formData,
                      details: { ...formData.details, notes: e.target.value }
                    })}
                    className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100"
                    rows={3}
                    placeholder="Additional notes about this performance..."
                  />
                </div>

                <div className="flex space-x-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setShowAddForm(false)}
                    className="flex-1"
                    disabled={isLoading}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    className="flex-1"
                    disabled={isLoading}
                  >
                    {isLoading ? 'Recording...' : 'Record Performance'}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>
      )}
    </section>
  )
}
