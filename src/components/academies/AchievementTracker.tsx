'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { MediaUpload, MediaFile } from '@/components/ui/MediaUpload'
import { addAchievement } from '@/app/actions/academies'

interface Achievement {
  id: number
  type: string
  title: string
  description: string
  level: string
  achievedAt: Date
  mediaUrls: string[]
  event?: {
    title: string
    type: string
    startDate: Date
  }
}

interface AchievementTrackerProps {
  memberId: number
  achievements: Achievement[]
  canRecord?: boolean
}

export function AchievementTracker({ memberId, achievements, canRecord = false }: AchievementTrackerProps) {
  const [showAddForm, setShowAddForm] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [formData, setFormData] = useState({
    type: '',
    title: '',
    description: '',
    level: '',
    mediaUrls: [] as string[]
  })

  const achievementTypes = [
    'Goal Scored',
    'Match Won',
    'Tournament Victory',
    'Skill Improvement',
    'Team Captain',
    'Best Player',
    'Most Improved',
    'Fair Play Award',
    'Training Milestone',
    'Academic Achievement',
    'Leadership Award',
    'Community Service'
  ]

  const achievementLevels = [
    'Bronze',
    'Silver',
    'Gold',
    'Platinum',
    'Local',
    'Regional',
    'National',
    'International'
  ]

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      const result = await addAchievement({
        academyMemberId: memberId,
        type: formData.type,
        title: formData.title,
        description: formData.description,
        level: formData.level,
        mediaUrls: formData.mediaUrls,
        awardedBy: 'Academy Coach' // Default value, could be made dynamic
      })

      if (result.success) {
        setShowAddForm(false)
        setFormData({
          type: '',
          title: '',
          description: '',
          level: '',
          mediaUrls: []
        })
        // Refresh the page or update the achievements list
        window.location.reload()
      } else {
        alert(result.error || 'Failed to add achievement')
      }
    } catch (error) {
      console.error('Error adding achievement:', error)
      alert('Failed to add achievement')
    } finally {
      setIsLoading(false)
    }
  }

  const handleMediaUpload = (files: MediaFile[]) => {
    const urls = files.map(file => file.url)
    setFormData({
      ...formData,
      mediaUrls: [...formData.mediaUrls, ...urls]
    })
  }

  const removeMedia = (index: number) => {
    setFormData({
      ...formData,
      mediaUrls: formData.mediaUrls.filter((_, i) => i !== index)
    })
  }

  const getAchievementIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'goal scored': return '⚽'
      case 'match won': return '🏆'
      case 'tournament victory': return '🥇'
      case 'skill improvement': return '📈'
      case 'team captain': return '👑'
      case 'best player': return '⭐'
      case 'most improved': return '🚀'
      case 'fair play award': return '🤝'
      case 'training milestone': return '🎯'
      case 'academic achievement': return '📚'
      case 'leadership award': return '👨‍💼'
      case 'community service': return '❤️'
      default: return '🏅'
    }
  }

  const getLevelColor = (level: string) => {
    switch (level.toLowerCase()) {
      case 'bronze': return 'bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-200'
      case 'silver': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
      case 'gold': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
      case 'platinum': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
      case 'local': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      case 'regional': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
      case 'national': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
      case 'international': return 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200'
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    }
  }

  return (
    <section className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100">
            Achievements & Awards
          </h2>
          <p className="text-neutral-600 dark:text-neutral-400">
            Celebrate milestones and accomplishments
          </p>
        </div>
        {canRecord && (
          <Button onClick={() => setShowAddForm(true)}>
            Add Achievement
          </Button>
        )}
      </div>

      {achievements.length > 0 ? (
        <div className="grid gap-6 md:grid-cols-2">
          {achievements.map((achievement) => (
            <Card key={achievement.id} variant="elevated">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="text-3xl">
                      {getAchievementIcon(achievement.type)}
                    </div>
                    <div>
                      <CardTitle className="text-lg">{achievement.title}</CardTitle>
                      <p className="text-sm text-neutral-600 dark:text-neutral-400">
                        {achievement.type}
                      </p>
                    </div>
                  </div>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getLevelColor(achievement.level)}`}>
                    {achievement.level}
                  </span>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <p className="text-neutral-700 dark:text-neutral-300">
                    {achievement.description}
                  </p>

                  {achievement.mediaUrls.length > 0 && (
                    <div className="grid grid-cols-2 gap-2">
                      {achievement.mediaUrls.slice(0, 4).map((url, index) => (
                        <div key={index} className="relative">
                          {url.includes('video') || url.includes('.mp4') ? (
                            <video
                              src={url}
                              className="w-full h-24 object-cover rounded"
                              controls
                              preload="metadata"
                            />
                          ) : (
                            <img
                              src={url}
                              alt="Achievement"
                              className="w-full h-24 object-cover rounded"
                            />
                          )}
                          {index === 3 && achievement.mediaUrls.length > 4 && (
                            <div className="absolute inset-0 bg-black/50 flex items-center justify-center rounded">
                              <span className="text-white font-medium">
                                +{achievement.mediaUrls.length - 4}
                              </span>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  )}

                  <div className="flex items-center justify-between text-sm text-neutral-500">
                    <span>
                      {new Date(achievement.achievedAt).toLocaleDateString()}
                    </span>
                    {achievement.event && (
                      <span>
                        {achievement.event.title}
                      </span>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <Card>
          <CardContent className="text-center py-12">
            <div className="text-4xl mb-4">🏆</div>
            <h3 className="text-lg font-medium text-neutral-900 dark:text-neutral-100 mb-2">
              No Achievements Yet
            </h3>
            <p className="text-neutral-600 dark:text-neutral-400 mb-6">
              Start recording achievements and milestones to celebrate progress.
            </p>
            {canRecord && (
              <Button onClick={() => setShowAddForm(true)}>
                Add First Achievement
              </Button>
            )}
          </CardContent>
        </Card>
      )}

      {/* Add Achievement Modal */}
      {showAddForm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <CardHeader>
              <CardTitle>Add Achievement</CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2">
                      Achievement Type
                    </label>
                    <select
                      value={formData.type}
                      onChange={(e) => setFormData({ ...formData, type: e.target.value })}
                      className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100"
                      required
                    >
                      <option value="">Select type...</option>
                      {achievementTypes.map((type) => (
                        <option key={type} value={type}>{type}</option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2">
                      Level
                    </label>
                    <select
                      value={formData.level}
                      onChange={(e) => setFormData({ ...formData, level: e.target.value })}
                      className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100"
                      required
                    >
                      <option value="">Select level...</option>
                      {achievementLevels.map((level) => (
                        <option key={level} value={level}>{level}</option>
                      ))}
                    </select>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2">
                    Title
                  </label>
                  <input
                    type="text"
                    value={formData.title}
                    onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                    className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100"
                    placeholder="Achievement title..."
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2">
                    Description
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100"
                    rows={3}
                    placeholder="Describe the achievement..."
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2">
                    Photos & Videos
                  </label>
                  <MediaUpload
                    onUpload={handleMediaUpload}
                    maxFiles={10}
                    allowImages={true}
                    allowVideos={true}
                  />
                  
                  {formData.mediaUrls.length > 0 && (
                    <div className="mt-4 grid grid-cols-3 gap-2">
                      {formData.mediaUrls.map((url, index) => (
                        <div key={index} className="relative">
                          {url.includes('video') || url.includes('.mp4') ? (
                            <video
                              src={url}
                              className="w-full h-20 object-cover rounded"
                              controls
                              preload="metadata"
                            />
                          ) : (
                            <img
                              src={url}
                              alt="Achievement media"
                              className="w-full h-20 object-cover rounded"
                            />
                          )}
                          <button
                            type="button"
                            onClick={() => removeMedia(index)}
                            className="absolute top-1 right-1 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs hover:bg-red-600"
                          >
                            ×
                          </button>
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                <div className="flex space-x-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setShowAddForm(false)}
                    className="flex-1"
                    disabled={isLoading}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    className="flex-1"
                    disabled={isLoading}
                  >
                    {isLoading ? 'Adding...' : 'Add Achievement'}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>
      )}
    </section>
  )
}
