'use client'

import { useState } from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { PerformanceTracker } from './PerformanceTracker'
import { AchievementTracker } from './AchievementTracker'
import { AcademyMediaManager } from './AcademyMediaManager'
import { ProgressDashboard } from './ProgressDashboard'

interface MemberProfileProps {
  member: {
    id: number
    academyId: number
    user: {
      firstName: string
      lastName: string
      avatar?: string | null
      email: string
    }
    academy: {
      name: string
      sport: string
      logo?: string | null
    }
    membershipType: string
    status: string
    position?: string | null
    skillLevel: string
    talentVideos: string[]
    personalInfo?: {
      dateOfBirth: string
      height: string
      weight: string
      dominantFoot: string
      previousExperience: string
      goals: string
    } | null
    medicalInfo?: {
      allergies: string
      medications: string
      injuries: string
      doctorContact: string
    } | null
    emergencyContact: {
      name: string
      phone: string
      relationship: string
    }
    joinedAt: Date
    performances: Array<{
      id: number
      metricType: string
      value: number
      unit: string
      recordedAt: Date
      details?: any
    }>
    achievements: Array<{
      id: number
      type: string
      title: string
      description: string
      level: string
      achievedAt: Date
      mediaUrls: string[]
    }>
  }
}

export function MemberProfile({ member }: MemberProfileProps) {
  const [activeTab, setActiveTab] = useState('overview')

  const calculateAge = (dateOfBirth: string) => {
    const today = new Date()
    const birthDate = new Date(dateOfBirth)
    let age = today.getFullYear() - birthDate.getFullYear()
    const monthDiff = today.getMonth() - birthDate.getMonth()
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--
    }
    return age
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      case 'inactive': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
      case 'suspended': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
      case 'graduated': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    }
  }

  const tabs = [
    { id: 'overview', label: 'Overview' },
    { id: 'performance', label: 'Performance' },
    { id: 'achievements', label: 'Achievements' },
    { id: 'media', label: 'Media Gallery' },
    { id: 'medical', label: 'Medical Info' }
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-primary-600 to-primary-700 rounded-lg p-6 text-white">
        <div className="flex items-center space-x-4">
          <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center">
            {member.user.avatar ? (
              <img
                src={member.user.avatar}
                alt={`${member.user.firstName} ${member.user.lastName}`}
                className="w-full h-full rounded-full object-cover"
              />
            ) : (
              <span className="text-2xl font-bold">
                {member.user.firstName[0]}{member.user.lastName[0]}
              </span>
            )}
          </div>
          <div className="flex-1">
            <h1 className="text-2xl font-bold">
              {member.user.firstName} {member.user.lastName}
            </h1>
            <p className="text-primary-100">
              {member.academy.name} • {member.position || 'No position set'}
            </p>
            <div className="flex items-center space-x-4 mt-2">
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(member.status)}`}>
                {member.status.charAt(0).toUpperCase() + member.status.slice(1)}
              </span>
              <span className="text-primary-100 text-sm">
                Member since {new Date(member.joinedAt).toLocaleDateString()}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="border-b border-neutral-200 dark:border-neutral-700">
        <nav className="flex space-x-8">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                  : 'border-transparent text-neutral-500 hover:text-neutral-700 dark:text-neutral-400 dark:hover:text-neutral-300'
              }`}
            >
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <div className="space-y-6">
          {/* Progress Dashboard */}
          <ProgressDashboard member={member} />

          {/* Personal Information Grid */}
          <div className="grid gap-6 md:grid-cols-2">
            {/* Personal Information */}
            <Card>
            <CardHeader>
              <CardTitle>Personal Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {member.personalInfo && (
                <>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-neutral-600 dark:text-neutral-400">Age</label>
                      <p className="text-neutral-900 dark:text-neutral-100">
                        {calculateAge(member.personalInfo.dateOfBirth)} years
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-neutral-600 dark:text-neutral-400">Height</label>
                      <p className="text-neutral-900 dark:text-neutral-100">
                        {member.personalInfo.height} cm
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-neutral-600 dark:text-neutral-400">Weight</label>
                      <p className="text-neutral-900 dark:text-neutral-100">
                        {member.personalInfo.weight} kg
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-neutral-600 dark:text-neutral-400">Dominant Foot</label>
                      <p className="text-neutral-900 dark:text-neutral-100">
                        {member.personalInfo.dominantFoot || 'Not specified'}
                      </p>
                    </div>
                  </div>
                  {member.personalInfo.goals && (
                    <div>
                      <label className="text-sm font-medium text-neutral-600 dark:text-neutral-400">Goals</label>
                      <p className="text-neutral-900 dark:text-neutral-100 mt-1">
                        {member.personalInfo.goals}
                      </p>
                    </div>
                  )}
                </>
              )}
            </CardContent>
          </Card>

          {/* Academy Information */}
          <Card>
            <CardHeader>
              <CardTitle>Academy Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-neutral-600 dark:text-neutral-400">Membership Type</label>
                  <p className="text-neutral-900 dark:text-neutral-100">
                    {member.membershipType.charAt(0).toUpperCase() + member.membershipType.slice(1)}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-neutral-600 dark:text-neutral-400">Skill Level</label>
                  <p className="text-neutral-900 dark:text-neutral-100">
                    {member.skillLevel.charAt(0).toUpperCase() + member.skillLevel.slice(1)}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-neutral-600 dark:text-neutral-400">Position</label>
                  <p className="text-neutral-900 dark:text-neutral-100">
                    {member.position || 'Not specified'}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-neutral-600 dark:text-neutral-400">Status</label>
                  <p className="text-neutral-900 dark:text-neutral-100">
                    {member.status.charAt(0).toUpperCase() + member.status.slice(1)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Emergency Contact */}
          <Card>
            <CardHeader>
              <CardTitle>Emergency Contact</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-neutral-600 dark:text-neutral-400">Name</label>
                  <p className="text-neutral-900 dark:text-neutral-100">
                    {member.emergencyContact.name}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-neutral-600 dark:text-neutral-400">Relationship</label>
                  <p className="text-neutral-900 dark:text-neutral-100">
                    {member.emergencyContact.relationship}
                  </p>
                </div>
                <div className="col-span-2">
                  <label className="text-sm font-medium text-neutral-600 dark:text-neutral-400">Phone</label>
                  <p className="text-neutral-900 dark:text-neutral-100">
                    {member.emergencyContact.phone}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Quick Stats */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Stats</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary-600">
                    {member.performances.length}
                  </div>
                  <div className="text-sm text-neutral-600 dark:text-neutral-400">
                    Performance Records
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary-600">
                    {member.achievements.length}
                  </div>
                  <div className="text-sm text-neutral-600 dark:text-neutral-400">
                    Achievements
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary-600">
                    {member.talentVideos.length}
                  </div>
                  <div className="text-sm text-neutral-600 dark:text-neutral-400">
                    Talent Videos
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary-600">
                    {Math.floor((new Date().getTime() - new Date(member.joinedAt).getTime()) / (1000 * 60 * 60 * 24))}
                  </div>
                  <div className="text-sm text-neutral-600 dark:text-neutral-400">
                    Days as Member
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
          </div>
        </div>
      )}

      {/* Performance Tab */}
      {activeTab === 'performance' && (
        <PerformanceTracker
          memberId={member.id}
          performances={member.performances}
          canRecord={true} // TODO: Add proper permission check
        />
      )}

      {/* Achievements Tab */}
      {activeTab === 'achievements' && (
        <AchievementTracker
          memberId={member.id}
          achievements={member.achievements}
          canRecord={true} // TODO: Add proper permission check
        />
      )}

      {/* Media Gallery Tab */}
      {activeTab === 'media' && (
        <AcademyMediaManager
          academyId={member.academyId}
          academyName={member.academy.name}
          memberId={member.id}
          canManage={true} // TODO: Add proper permission check
        />
      )}

      {/* Medical Info Tab */}
      {activeTab === 'medical' && (
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Medical Information</CardTitle>
            </CardHeader>
            <CardContent>
              {member.medicalInfo ? (
                <div className="grid gap-6 md:grid-cols-2">
                  <div>
                    <label className="text-sm font-medium text-neutral-600 dark:text-neutral-400">Allergies</label>
                    <p className="text-neutral-900 dark:text-neutral-100 mt-1">
                      {member.medicalInfo.allergies || 'None reported'}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-neutral-600 dark:text-neutral-400">Current Medications</label>
                    <p className="text-neutral-900 dark:text-neutral-100 mt-1">
                      {member.medicalInfo.medications || 'None reported'}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-neutral-600 dark:text-neutral-400">Previous Injuries</label>
                    <p className="text-neutral-900 dark:text-neutral-100 mt-1">
                      {member.medicalInfo.injuries || 'None reported'}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-neutral-600 dark:text-neutral-400">Doctor Contact</label>
                    <p className="text-neutral-900 dark:text-neutral-100 mt-1">
                      {member.medicalInfo.doctorContact || 'Not provided'}
                    </p>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <div className="text-neutral-400 mb-2">🏥</div>
                  <p className="text-neutral-600 dark:text-neutral-400">
                    No medical information provided
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
