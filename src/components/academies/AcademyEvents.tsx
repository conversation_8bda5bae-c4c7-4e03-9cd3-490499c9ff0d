import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'

interface AcademyEventsProps {
  academy: {
    id: number
    name: string
    events: Array<{
      id: number
      title: string
      description: string
      type: string
      startDate: Date
      endDate: Date
      location: string
      isPublic: boolean
    }>
  }
}

export function AcademyEvents({ academy }: AcademyEventsProps) {
  const upcomingEvents = academy.events.filter(event =>
    new Date(event.startDate) > new Date() && event.isPublic
  )

  const getEventTypeIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'tournament': return '🏆'
      case 'match': return '⚽'
      case 'showcase': return '🌟'
      case 'training_camp': return '🏕️'
      case 'trial': return '🎯'
      case 'graduation': return '🎓'
      default: return '📅'
    }
  }

  const getEventTypeColor = (type: string) => {
    switch (type.toLowerCase()) {
      case 'tournament': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-200'
      case 'match': return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-200'
      case 'showcase': return 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-200'
      case 'training_camp': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-200'
      case 'trial': return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-200'
      case 'graduation': return 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-200'
      default: return 'bg-neutral-100 text-neutral-800 dark:bg-neutral-900/30 dark:text-neutral-200'
    }
  }

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      weekday: 'long',
      month: 'long',
      day: 'numeric',
      year: 'numeric'
    })
  }

  const formatTime = (date: Date) => {
    return new Date(date).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const isEventUpcoming = (event: any) => {
    const now = new Date()
    const startDate = new Date(event.startDate)
    return startDate > now
  }

  return (
    <section className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100 md:text-3xl">
          Upcoming Events
        </h2>
        <p className="mt-2 text-neutral-600 dark:text-neutral-400">
          Competitions, showcases, and special events at {academy.name}
        </p>
      </div>

      {upcomingEvents.length === 0 ? (
        <Card variant="elevated" className="text-center py-12">
          <CardContent>
            <div className="text-6xl mb-4">🎪</div>
            <h3 className="text-xl font-semibold text-neutral-900 dark:text-neutral-100 mb-2">
              No Upcoming Events
            </h3>
            <p className="text-neutral-600 dark:text-neutral-400 mb-6">
              Stay tuned for exciting events, tournaments, and showcases
            </p>
            <Button>Get Notified</Button>
          </CardContent>
        </Card>
      ) : (
        <>
          <div className="grid gap-6 md:grid-cols-2">
            {upcomingEvents.map((event) => (
              <Card key={event.id} variant="elevated" className="overflow-hidden">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <CardTitle className="flex items-center space-x-2">
                        <span className="text-2xl">{getEventTypeIcon(event.type)}</span>
                        <span className="line-clamp-1">{event.title}</span>
                      </CardTitle>
                      <div className="flex items-center space-x-2 mt-2">
                        <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${getEventTypeColor(event.type)}`}>
                          {event.type.replace('_', ' ')}
                        </span>
                        <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                          isEventUpcoming(event)
                            ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-200'
                            : 'bg-neutral-100 text-neutral-800 dark:bg-neutral-900/30 dark:text-neutral-200'
                        }`}>
                          {isEventUpcoming(event) ? 'Upcoming' : 'Past Event'}
                        </span>
                        {event.isPublic && (
                          <span className="inline-flex items-center rounded-full bg-blue-100 dark:bg-blue-900/30 px-2.5 py-0.5 text-xs font-medium text-blue-800 dark:text-blue-200">
                            Public
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                </CardHeader>
                
                <CardContent className="space-y-4">
                  <p className="text-sm text-neutral-600 dark:text-neutral-400 line-clamp-2">
                    {event.description}
                  </p>
                  
                  <div className="space-y-3">
                    <div className="flex items-center space-x-2">
                      <span className="text-neutral-600 dark:text-neutral-400">📅</span>
                      <div>
                        <div className="font-medium text-neutral-900 dark:text-neutral-100">
                          {formatDate(event.startDate)}
                        </div>
                        <div className="text-sm text-neutral-600 dark:text-neutral-400">
                          {formatTime(event.startDate)}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <span className="text-neutral-600 dark:text-neutral-400">📍</span>
                      <span className="text-sm text-neutral-900 dark:text-neutral-100">
                        {event.location}
                      </span>
                    </div>

                    <div className="flex items-center space-x-2">
                      <span className="text-neutral-600 dark:text-neutral-400">⏱️</span>
                      <div>
                        <span className="text-sm text-neutral-900 dark:text-neutral-100">
                          Duration:
                        </span>
                        <span className="text-sm font-medium text-neutral-900 dark:text-neutral-100">
                          {formatDate(event.startDate)} - {formatDate(event.endDate)}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="flex space-x-2">
                    {isEventUpcoming(event) ? (
                      <Button size="sm" className="flex-1">
                        Register Interest
                      </Button>
                    ) : (
                      <Button size="sm" variant="outline" className="flex-1">
                        View Details
                      </Button>
                    )}
                    <Button size="sm" variant="outline">
                      Share Event
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {upcomingEvents.length > 4 && (
            <div className="text-center">
              <Button variant="outline">
                View All Events
              </Button>
            </div>
          )}

          {/* Events Summary */}
          <Card variant="elevated">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <span>📈</span>
                <span>Events Overview</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary-600 dark:text-primary-400">
                    {upcomingEvents.filter(e => e.type === 'tournament').length}
                  </div>
                  <div className="text-sm text-neutral-600 dark:text-neutral-400">
                    Tournaments
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary-600 dark:text-primary-400">
                    {upcomingEvents.filter(e => e.type === 'match').length}
                  </div>
                  <div className="text-sm text-neutral-600 dark:text-neutral-400">
                    Matches
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary-600 dark:text-primary-400">
                    {upcomingEvents.filter(e => e.type === 'showcase').length}
                  </div>
                  <div className="text-sm text-neutral-600 dark:text-neutral-400">
                    Showcases
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary-600 dark:text-primary-400">
                    {upcomingEvents.filter(e => e.isPublic).length}
                  </div>
                  <div className="text-sm text-neutral-600 dark:text-neutral-400">
                    Public Events
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </>
      )}
    </section>
  )
}
