import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/Card'

interface AcademyAboutProps {
  academy: {
    id: number
    name: string
    sport: string
    description: string
    facilities: any
    programs: any
  }
  content: any
}

export function AcademyAbout({ academy, content }: AcademyAboutProps) {
  return (
    <section className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100 md:text-3xl">
          About {academy.name}
        </h2>
        <p className="mt-2 text-neutral-600 dark:text-neutral-400">
          Learn more about our academy, facilities, and training philosophy
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Academy Description */}
        <Card variant="elevated">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <span>📖</span>
              <span>Our Story</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-neutral-600 dark:text-neutral-400">
              {academy.description}
            </p>
            
            {content?.mission && (
              <div>
                <h4 className="font-semibold text-neutral-900 dark:text-neutral-100 mb-2">
                  Our Mission
                </h4>
                <p className="text-sm text-neutral-600 dark:text-neutral-400">
                  {content.mission}
                </p>
              </div>
            )}

            {content?.vision && (
              <div>
                <h4 className="font-semibold text-neutral-900 dark:text-neutral-100 mb-2">
                  Our Vision
                </h4>
                <p className="text-sm text-neutral-600 dark:text-neutral-400">
                  {content.vision}
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Training Philosophy */}
        <Card variant="elevated">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <span>🎯</span>
              <span>Training Philosophy</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {content?.philosophy ? (
              <p className="text-neutral-600 dark:text-neutral-400">
                {content.philosophy}
              </p>
            ) : (
              <p className="text-neutral-600 dark:text-neutral-400">
                We believe in developing well-rounded athletes through comprehensive training that focuses on technical skills, physical fitness, mental strength, and character development. Our approach combines traditional coaching methods with modern sports science to maximize each athlete's potential.
              </p>
            )}

            <div className="space-y-2">
              <h4 className="font-semibold text-neutral-900 dark:text-neutral-100">
                Core Values
              </h4>
              <div className="space-y-1">
                {content?.values ? (
                  content.values.map((value: string, index: number) => (
                    <div key={index} className="flex items-start space-x-2">
                      <span className="text-primary-600 dark:text-primary-400">•</span>
                      <span className="text-sm text-neutral-600 dark:text-neutral-400">
                        {value}
                      </span>
                    </div>
                  ))
                ) : (
                  <>
                    <div className="flex items-start space-x-2">
                      <span className="text-primary-600 dark:text-primary-400">•</span>
                      <span className="text-sm text-neutral-600 dark:text-neutral-400">
                        Excellence in everything we do
                      </span>
                    </div>
                    <div className="flex items-start space-x-2">
                      <span className="text-primary-600 dark:text-primary-400">•</span>
                      <span className="text-sm text-neutral-600 dark:text-neutral-400">
                        Respect for teammates, opponents, and officials
                      </span>
                    </div>
                    <div className="flex items-start space-x-2">
                      <span className="text-primary-600 dark:text-primary-400">•</span>
                      <span className="text-sm text-neutral-600 dark:text-neutral-400">
                        Commitment to continuous improvement
                      </span>
                    </div>
                    <div className="flex items-start space-x-2">
                      <span className="text-primary-600 dark:text-primary-400">•</span>
                      <span className="text-sm text-neutral-600 dark:text-neutral-400">
                        Teamwork and collaboration
                      </span>
                    </div>
                  </>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Facilities */}
        <Card variant="elevated">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <span>🏟️</span>
              <span>Our Facilities</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {academy.facilities && typeof academy.facilities === 'object' ? (
              <div className="space-y-3">
                {Object.entries(academy.facilities).map(([key, value]) => (
                  <div key={key} className="flex items-start space-x-2">
                    <span className="text-primary-600 dark:text-primary-400">•</span>
                    <div>
                      <span className="text-sm font-medium text-neutral-900 dark:text-neutral-100 capitalize">
                        {key.replace(/([A-Z])/g, ' $1').trim()}
                      </span>
                      {typeof value === 'string' && (
                        <p className="text-xs text-neutral-600 dark:text-neutral-400">
                          {value}
                        </p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="space-y-2">
                <div className="flex items-start space-x-2">
                  <span className="text-primary-600 dark:text-primary-400">•</span>
                  <span className="text-sm text-neutral-600 dark:text-neutral-400">
                    Full-size training pitch with modern drainage system
                  </span>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="text-primary-600 dark:text-primary-400">•</span>
                  <span className="text-sm text-neutral-600 dark:text-neutral-400">
                    State-of-the-art fitness and conditioning center
                  </span>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="text-primary-600 dark:text-primary-400">•</span>
                  <span className="text-sm text-neutral-600 dark:text-neutral-400">
                    Medical and physiotherapy facilities
                  </span>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="text-primary-600 dark:text-primary-400">•</span>
                  <span className="text-sm text-neutral-600 dark:text-neutral-400">
                    Video analysis room for performance review
                  </span>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="text-primary-600 dark:text-primary-400">•</span>
                  <span className="text-sm text-neutral-600 dark:text-neutral-400">
                    Changing rooms and equipment storage
                  </span>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Coaching Staff */}
        <Card variant="elevated">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <span>👨‍🏫</span>
              <span>Coaching Staff</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {content?.coaches ? (
              <div className="space-y-3">
                {content.coaches.map((coach: any, index: number) => (
                  <div key={index} className="border-l-2 border-primary-200 dark:border-primary-800 pl-3">
                    <h4 className="font-semibold text-neutral-900 dark:text-neutral-100">
                      {coach.name}
                    </h4>
                    <p className="text-sm text-primary-600 dark:text-primary-400">
                      {coach.position}
                    </p>
                    {coach.experience && (
                      <p className="text-xs text-neutral-600 dark:text-neutral-400">
                        {coach.experience}
                      </p>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div className="space-y-3">
                <div className="border-l-2 border-primary-200 dark:border-primary-800 pl-3">
                  <h4 className="font-semibold text-neutral-900 dark:text-neutral-100">
                    Professional Coaching Team
                  </h4>
                  <p className="text-sm text-primary-600 dark:text-primary-400">
                    Licensed and experienced coaches
                  </p>
                  <p className="text-xs text-neutral-600 dark:text-neutral-400">
                    Our coaching staff includes former professional players and certified trainers with extensive experience in youth development and performance optimization.
                  </p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </section>
  )
}
