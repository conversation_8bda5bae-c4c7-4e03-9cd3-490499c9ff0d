'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { MediaUpload } from '@/components/ui/MediaUpload'
import { registerForAcademy } from '@/app/actions/academies'

interface AcademyRegistrationProps {
  academy: {
    id: number
    name: string
    sport: string
    level: string
  }
}

interface MediaFile {
  url: string
  publicId?: string
  type: 'image' | 'video'
  fileName: string
  size: number
}

export function AcademyRegistration({ academy }: AcademyRegistrationProps) {
  const [currentStep, setCurrentStep] = useState(0)
  const [isLoading, setIsLoading] = useState(false)
  const [formData, setFormData] = useState({
    // Personal Information
    personalInfo: {
      dateOfBirth: '',
      height: '',
      weight: '',
      dominantFoot: '',
      previousExperience: '',
      goals: ''
    },
    // Academy Specific
    membershipType: 'regular',
    position: '',
    skillLevel: 'beginner',
    talentVideos: [] as string[],
    // Medical Information
    medicalInfo: {
      allergies: '',
      medications: '',
      injuries: '',
      doctorContact: ''
    },
    // Emergency Contact
    emergencyContact: {
      name: '',
      phone: '',
      relationship: ''
    }
  })

  const steps = [
    { title: 'Personal Info', description: 'Basic information about you' },
    { title: 'Academy Details', description: 'Position and skill level' },
    { title: 'Talent Videos', description: 'Showcase your skills' },
    { title: 'Medical Info', description: 'Health and safety information' },
    { title: 'Emergency Contact', description: 'Emergency contact details' }
  ]

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      const result = await registerForAcademy({
        academyId: academy.id,
        membershipType: formData.membershipType,
        position: formData.position || undefined,
        skillLevel: formData.skillLevel,
        talentVideos: formData.talentVideos,
        medicalInfo: formData.medicalInfo,
        emergencyContact: formData.emergencyContact,
        personalInfo: formData.personalInfo
      })

      if (result.success) {
        alert('Successfully registered for the academy!')
        resetForm()
      } else {
        alert(result.error || 'Failed to register')
      }
    } catch (error) {
      console.error('Registration error:', error)
      alert('An error occurred during registration')
    } finally {
      setIsLoading(false)
    }
  }

  const resetForm = () => {
    setCurrentStep(0)
    setFormData({
      personalInfo: {
        dateOfBirth: '',
        height: '',
        weight: '',
        dominantFoot: '',
        previousExperience: '',
        goals: ''
      },
      membershipType: 'regular',
      position: '',
      skillLevel: 'beginner',
      talentVideos: [],
      medicalInfo: {
        allergies: '',
        medications: '',
        injuries: '',
        doctorContact: ''
      },
      emergencyContact: {
        name: '',
        phone: '',
        relationship: ''
      }
    })
  }

  const handleInputChange = (field: string, value: string) => {
    if (field.startsWith('personalInfo.')) {
      const infoField = field.split('.')[1]
      setFormData(prev => ({
        ...prev,
        personalInfo: {
          ...prev.personalInfo,
          [infoField]: value
        }
      }))
    } else if (field.startsWith('medicalInfo.')) {
      const medicalField = field.split('.')[1]
      setFormData(prev => ({
        ...prev,
        medicalInfo: {
          ...prev.medicalInfo,
          [medicalField]: value
        }
      }))
    } else if (field.startsWith('emergencyContact.')) {
      const contactField = field.split('.')[1]
      setFormData(prev => ({
        ...prev,
        emergencyContact: {
          ...prev.emergencyContact,
          [contactField]: value
        }
      }))
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }))
    }
  }

  const handleMediaUpload = (files: MediaFile[]) => {
    const videoUrls = files
      .filter(file => file.type === 'video')
      .map(file => file.url)

    setFormData(prev => ({
      ...prev,
      talentVideos: [...prev.talentVideos, ...videoUrls]
    }))
  }

  const removeVideo = (index: number) => {
    setFormData(prev => ({
      ...prev,
      talentVideos: prev.talentVideos.filter((_, i) => i !== index)
    }))
  }

  const nextStep = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1)
    }
  }

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1)
    }
  }

  const isStepValid = () => {
    switch (currentStep) {
      case 0: // Personal Info
        return formData.personalInfo.dateOfBirth && formData.personalInfo.height && formData.personalInfo.weight
      case 1: // Academy Details
        return formData.membershipType && formData.skillLevel
      case 2: // Talent Videos
        return true // Optional step
      case 3: // Medical Info
        return true // Optional step but recommended
      case 4: // Emergency Contact
        return formData.emergencyContact.name && formData.emergencyContact.phone && formData.emergencyContact.relationship
      default:
        return false
    }
  }

  return (
    <section className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100 md:text-3xl">
          Join {academy.name}
        </h2>
        <p className="mt-2 text-neutral-600 dark:text-neutral-400">
          Start your journey with us today
        </p>
      </div>

      <Card variant="elevated" className="mx-auto max-w-4xl">
        <CardHeader>
          <CardTitle className="text-center">
            Academy Registration
          </CardTitle>
          {currentStep > 0 && (
            <div className="mt-4">
              {/* Progress Bar */}
              <div className="flex items-center justify-between mb-4">
                {steps.map((step, index) => (
                  <div key={index} className="flex items-center">
                    <div
                      className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                        index <= currentStep
                          ? 'bg-primary-600 text-white'
                          : 'bg-neutral-200 dark:bg-neutral-700 text-neutral-600 dark:text-neutral-400'
                      }`}
                    >
                      {index + 1}
                    </div>
                    {index < steps.length - 1 && (
                      <div
                        className={`h-1 w-12 mx-2 ${
                          index < currentStep
                            ? 'bg-primary-600'
                            : 'bg-neutral-200 dark:bg-neutral-700'
                        }`}
                      />
                    )}
                  </div>
                ))}
              </div>
              <div className="text-center">
                <h3 className="font-semibold text-neutral-900 dark:text-neutral-100">
                  {steps[currentStep].title}
                </h3>
                <p className="text-sm text-neutral-600 dark:text-neutral-400">
                  {steps[currentStep].description}
                </p>
              </div>
            </div>
          )}
        </CardHeader>
        <CardContent>
          {currentStep === 0 && (
            <div className="text-center space-y-4">
              <p className="text-neutral-600 dark:text-neutral-400">
                Ready to take your {academy.sport} skills to the next level? Join our {academy.level} level academy and train with professional coaches.
              </p>
              <div className="grid gap-4 md:grid-cols-3">
                <div className="text-center">
                  <div className="text-2xl mb-2">🏆</div>
                  <h3 className="font-semibold text-neutral-900 dark:text-neutral-100">
                    Professional Training
                  </h3>
                  <p className="text-sm text-neutral-600 dark:text-neutral-400">
                    Learn from experienced coaches
                  </p>
                </div>
                <div className="text-center">
                  <div className="text-2xl mb-2">📊</div>
                  <h3 className="font-semibold text-neutral-900 dark:text-neutral-100">
                    Performance Tracking
                  </h3>
                  <p className="text-sm text-neutral-600 dark:text-neutral-400">
                    Monitor your progress
                  </p>
                </div>
                <div className="text-center">
                  <div className="text-2xl mb-2">🤝</div>
                  <h3 className="font-semibold text-neutral-900 dark:text-neutral-100">
                    Club Connections
                  </h3>
                  <p className="text-sm text-neutral-600 dark:text-neutral-400">
                    Direct path to professional clubs
                  </p>
                </div>
              </div>
              <Button onClick={() => setCurrentStep(1)} size="lg" className="w-full">
                Start Registration
              </Button>
            </div>
          )}
          {currentStep === 1 && (
            <form onSubmit={(e) => { e.preventDefault(); nextStep(); }} className="space-y-6">
              {/* Personal Information Step */}
              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2">
                    Date of Birth *
                  </label>
                  <input
                    type="date"
                    value={formData.personalInfo.dateOfBirth}
                    onChange={(e) => handleInputChange('personalInfo.dateOfBirth', e.target.value)}
                    className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2">
                    Height (cm) *
                  </label>
                  <input
                    type="number"
                    value={formData.personalInfo.height}
                    onChange={(e) => handleInputChange('personalInfo.height', e.target.value)}
                    className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100"
                    placeholder="175"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2">
                    Weight (kg) *
                  </label>
                  <input
                    type="number"
                    value={formData.personalInfo.weight}
                    onChange={(e) => handleInputChange('personalInfo.weight', e.target.value)}
                    className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100"
                    placeholder="70"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2">
                    Dominant Foot
                  </label>
                  <select
                    value={formData.personalInfo.dominantFoot}
                    onChange={(e) => handleInputChange('personalInfo.dominantFoot', e.target.value)}
                    className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100"
                  >
                    <option value="">Select...</option>
                    <option value="right">Right</option>
                    <option value="left">Left</option>
                    <option value="both">Both</option>
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2">
                  Previous Experience
                </label>
                <textarea
                  value={formData.personalInfo.previousExperience}
                  onChange={(e) => handleInputChange('personalInfo.previousExperience', e.target.value)}
                  className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100"
                  rows={3}
                  placeholder="Tell us about your previous football experience..."
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2">
                  Goals & Aspirations
                </label>
                <textarea
                  value={formData.personalInfo.goals}
                  onChange={(e) => handleInputChange('personalInfo.goals', e.target.value)}
                  className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100"
                  rows={3}
                  placeholder="What are your goals in football?"
                />
              </div>

              <div className="flex space-x-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setCurrentStep(0)}
                  className="flex-1"
                >
                  Back
                </Button>
                <Button
                  type="submit"
                  className="flex-1"
                  disabled={!isStepValid()}
                >
                  Next: Academy Details
                </Button>
              </div>
            </form>
          )}

          {currentStep === 2 && (
            <form onSubmit={(e) => { e.preventDefault(); nextStep(); }} className="space-y-6">
              {/* Academy Details Step */}
              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2">
                    Membership Type *
                  </label>
                  <select
                    value={formData.membershipType}
                    onChange={(e) => handleInputChange('membershipType', e.target.value)}
                    className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100"
                    required
                  >
                    <option value="regular">Regular Membership</option>
                    <option value="premium">Premium Membership</option>
                    <option value="trial">Trial Membership</option>
                    <option value="scholarship">Scholarship Application</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2">
                    Skill Level *
                  </label>
                  <select
                    value={formData.skillLevel}
                    onChange={(e) => handleInputChange('skillLevel', e.target.value)}
                    className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100"
                    required
                  >
                    <option value="beginner">Beginner</option>
                    <option value="intermediate">Intermediate</option>
                    <option value="advanced">Advanced</option>
                    <option value="elite">Elite</option>
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2">
                  Preferred Position
                </label>
                <select
                  value={formData.position}
                  onChange={(e) => handleInputChange('position', e.target.value)}
                  className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100"
                >
                  <option value="">Select position...</option>
                  <option value="goalkeeper">Goalkeeper</option>
                  <option value="defender">Defender</option>
                  <option value="midfielder">Midfielder</option>
                  <option value="forward">Forward</option>
                  <option value="striker">Striker</option>
                  <option value="winger">Winger</option>
                </select>
              </div>

              <div className="flex space-x-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={prevStep}
                  className="flex-1"
                >
                  Back
                </Button>
                <Button
                  type="submit"
                  className="flex-1"
                  disabled={!isStepValid()}
                >
                  Next: Talent Videos
                </Button>
              </div>
            </form>
          )}

          {currentStep === 3 && (
            <form onSubmit={(e) => { e.preventDefault(); nextStep(); }} className="space-y-6">
              {/* Talent Videos Step */}
              <div>
                <h3 className="text-lg font-semibold text-neutral-900 dark:text-neutral-100 mb-4">
                  Showcase Your Talent
                </h3>
                <p className="text-neutral-600 dark:text-neutral-400 mb-6">
                  Upload videos that showcase your skills. This helps our coaches understand your current level and potential.
                </p>

                <MediaUpload
                  onUpload={handleMediaUpload}
                  maxFiles={5}
                  allowImages={false}
                  allowVideos={true}
                  className="mb-6"
                />

                {formData.talentVideos.length > 0 && (
                  <div className="space-y-4">
                    <h4 className="font-medium text-neutral-900 dark:text-neutral-100">
                      Uploaded Videos ({formData.talentVideos.length})
                    </h4>
                    <div className="grid gap-4 md:grid-cols-2">
                      {formData.talentVideos.map((videoUrl, index) => (
                        <div key={index} className="relative border border-neutral-200 dark:border-neutral-700 rounded-lg overflow-hidden">
                          <video
                            src={videoUrl}
                            className="w-full h-32 object-cover"
                            controls
                            preload="metadata"
                          />
                          <button
                            type="button"
                            onClick={() => removeVideo(index)}
                            className="absolute top-2 right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm hover:bg-red-600"
                          >
                            ×
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              <div className="flex space-x-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={prevStep}
                  className="flex-1"
                >
                  Back
                </Button>
                <Button
                  type="submit"
                  className="flex-1"
                >
                  Next: Medical Info
                </Button>
              </div>
            </form>
          )}

          {currentStep === 4 && (
            <form onSubmit={(e) => { e.preventDefault(); nextStep(); }} className="space-y-6">
              {/* Medical Information Step */}
              <div>
                <h3 className="text-lg font-semibold text-neutral-900 dark:text-neutral-100 mb-4">
                  Medical Information
                </h3>
                <p className="text-neutral-600 dark:text-neutral-400 mb-6">
                  This information helps us ensure your safety during training and activities.
                </p>
              </div>

              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2">
                    Allergies
                  </label>
                  <textarea
                    value={formData.medicalInfo.allergies}
                    onChange={(e) => handleInputChange('medicalInfo.allergies', e.target.value)}
                    className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100"
                    rows={3}
                    placeholder="List any allergies..."
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2">
                    Current Medications
                  </label>
                  <textarea
                    value={formData.medicalInfo.medications}
                    onChange={(e) => handleInputChange('medicalInfo.medications', e.target.value)}
                    className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100"
                    rows={3}
                    placeholder="List any medications..."
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2">
                  Previous Injuries
                </label>
                <textarea
                  value={formData.medicalInfo.injuries}
                  onChange={(e) => handleInputChange('medicalInfo.injuries', e.target.value)}
                  className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100"
                  rows={3}
                  placeholder="Describe any previous injuries..."
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2">
                  Doctor Contact
                </label>
                <input
                  type="text"
                  value={formData.medicalInfo.doctorContact}
                  onChange={(e) => handleInputChange('medicalInfo.doctorContact', e.target.value)}
                  className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100"
                  placeholder="Doctor's name and phone number"
                />
              </div>

              <div className="flex space-x-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={prevStep}
                  className="flex-1"
                >
                  Back
                </Button>
                <Button
                  type="submit"
                  className="flex-1"
                >
                  Next: Emergency Contact
                </Button>
              </div>
            </form>
          )}

          {currentStep === 5 && (
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Emergency Contact Step */}
              <div>
                <h3 className="text-lg font-semibold text-neutral-900 dark:text-neutral-100 mb-4">
                  Emergency Contact
                </h3>
                <p className="text-neutral-600 dark:text-neutral-400 mb-6">
                  Please provide emergency contact information for safety purposes.
                </p>
              </div>

              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2">
                    Contact Name *
                  </label>
                  <input
                    type="text"
                    value={formData.emergencyContact.name}
                    onChange={(e) => handleInputChange('emergencyContact.name', e.target.value)}
                    className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100"
                    placeholder="Full name"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2">
                    Phone Number *
                  </label>
                  <input
                    type="tel"
                    value={formData.emergencyContact.phone}
                    onChange={(e) => handleInputChange('emergencyContact.phone', e.target.value)}
                    className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100"
                    placeholder="+255 XXX XXX XXX"
                    required
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2">
                  Relationship *
                </label>
                <select
                  value={formData.emergencyContact.relationship}
                  onChange={(e) => handleInputChange('emergencyContact.relationship', e.target.value)}
                  className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100"
                  required
                >
                  <option value="">Select relationship...</option>
                  <option value="parent">Parent</option>
                  <option value="guardian">Guardian</option>
                  <option value="spouse">Spouse</option>
                  <option value="sibling">Sibling</option>
                  <option value="friend">Friend</option>
                  <option value="other">Other</option>
                </select>
              </div>

              <div className="bg-neutral-50 dark:bg-neutral-800 p-4 rounded-lg">
                <h4 className="font-medium text-neutral-900 dark:text-neutral-100 mb-2">
                  Registration Summary
                </h4>
                <div className="text-sm text-neutral-600 dark:text-neutral-400 space-y-1">
                  <p><strong>Academy:</strong> {academy.name}</p>
                  <p><strong>Membership:</strong> {formData.membershipType}</p>
                  <p><strong>Skill Level:</strong> {formData.skillLevel}</p>
                  {formData.position && <p><strong>Position:</strong> {formData.position}</p>}
                  <p><strong>Talent Videos:</strong> {formData.talentVideos.length} uploaded</p>
                </div>
              </div>

              <div className="flex space-x-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={prevStep}
                  className="flex-1"
                  disabled={isLoading}
                >
                  Back
                </Button>
                <Button
                  type="submit"
                  className="flex-1"
                  disabled={isLoading || !isStepValid()}
                >
                  {isLoading ? 'Registering...' : 'Complete Registration'}
                </Button>
              </div>
            </form>
          )}
        </CardContent>
      </Card>
    </section>
  )
}
