'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/Button'
import { ImagePreviewModal } from '@/components/ui/ImagePreviewModal'
import { AcademyMediaFile } from './AcademyMediaUpload'

interface AcademyMediaGalleryProps {
  media: AcademyMediaFile[]
  category?: 'talent' | 'achievement' | 'training' | 'event' | 'certificate' | 'all'
  showFilters?: boolean
  showUploadButton?: boolean
  onUploadClick?: () => void
  canManage?: boolean
  onDelete?: (mediaId: string) => void
  className?: string
}

export function AcademyMediaGallery({
  media,
  category = 'all',
  showFilters = true,
  showUploadButton = false,
  onUploadClick,
  canManage = false,
  onDelete,
  className = ''
}: AcademyMediaGalleryProps) {
  const [selectedCategory, setSelectedCategory] = useState<string>(category)
  const [previewOpen, setPreviewOpen] = useState(false)
  const [previewIndex, setPreviewIndex] = useState(0)
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')

  // Filter media based on selected category
  const filteredMedia = selectedCategory === 'all' 
    ? media 
    : media.filter(item => item.category === selectedCategory)

  // Get only images for preview modal
  const imageUrls = filteredMedia
    .filter(item => item.type === 'image')
    .map(item => item.url)

  const handleImageClick = (index: number) => {
    const imageIndex = filteredMedia
      .slice(0, index + 1)
      .filter(item => item.type === 'image').length - 1
    
    if (imageIndex >= 0) {
      setPreviewIndex(imageIndex)
      setPreviewOpen(true)
    }
  }

  const getCategoryIcon = (cat: string) => {
    switch (cat) {
      case 'talent':
        return '🏆'
      case 'achievement':
        return '🥇'
      case 'training':
        return '💪'
      case 'event':
        return '🎯'
      case 'certificate':
        return '📜'
      default:
        return '📁'
    }
  }

  const getCategoryColor = (cat: string) => {
    switch (cat) {
      case 'talent':
        return 'bg-purple-100 dark:bg-purple-900/20 text-purple-800 dark:text-purple-200'
      case 'achievement':
        return 'bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-200'
      case 'training':
        return 'bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-200'
      case 'event':
        return 'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-200'
      case 'certificate':
        return 'bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-200'
      default:
        return 'bg-neutral-100 dark:bg-neutral-900/20 text-neutral-800 dark:text-neutral-200'
    }
  }

  const categories = [
    { value: 'all', label: 'All Media', icon: '📁' },
    { value: 'talent', label: 'Talent', icon: '🏆' },
    { value: 'achievement', label: 'Achievements', icon: '🥇' },
    { value: 'training', label: 'Training', icon: '💪' },
    { value: 'event', label: 'Events', icon: '🎯' },
    { value: 'certificate', label: 'Certificates', icon: '📜' }
  ]

  if (media.length === 0) {
    return (
      <div className={`text-center py-12 ${className}`}>
        <div className="text-6xl mb-4">📸</div>
        <h3 className="text-lg font-semibold text-neutral-900 dark:text-neutral-100 mb-2">
          No media uploaded yet
        </h3>
        <p className="text-neutral-600 dark:text-neutral-400 mb-4">
          Start building your academy's media collection
        </p>
        {showUploadButton && onUploadClick && (
          <Button onClick={onUploadClick}>
            Upload Media
          </Button>
        )}
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-xl font-bold text-neutral-900 dark:text-neutral-100">
            Media Gallery
          </h2>
          <p className="text-sm text-neutral-600 dark:text-neutral-400">
            {filteredMedia.length} {filteredMedia.length === 1 ? 'item' : 'items'}
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          {/* View Mode Toggle */}
          <div className="flex rounded-lg border border-neutral-300 dark:border-neutral-600 overflow-hidden">
            <button
              onClick={() => setViewMode('grid')}
              className={`px-3 py-1 text-sm ${
                viewMode === 'grid'
                  ? 'bg-primary-500 text-white'
                  : 'bg-white dark:bg-neutral-800 text-neutral-700 dark:text-neutral-300 hover:bg-neutral-50 dark:hover:bg-neutral-700'
              }`}
            >
              Grid
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`px-3 py-1 text-sm ${
                viewMode === 'list'
                  ? 'bg-primary-500 text-white'
                  : 'bg-white dark:bg-neutral-800 text-neutral-700 dark:text-neutral-300 hover:bg-neutral-50 dark:hover:bg-neutral-700'
              }`}
            >
              List
            </button>
          </div>

          {showUploadButton && onUploadClick && (
            <Button onClick={onUploadClick} size="sm">
              Upload Media
            </Button>
          )}
        </div>
      </div>

      {/* Filters */}
      {showFilters && (
        <div className="flex flex-wrap gap-2">
          {categories.map((cat) => (
            <button
              key={cat.value}
              onClick={() => setSelectedCategory(cat.value)}
              className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                selectedCategory === cat.value
                  ? 'bg-primary-500 text-white'
                  : 'bg-neutral-100 dark:bg-neutral-800 text-neutral-700 dark:text-neutral-300 hover:bg-neutral-200 dark:hover:bg-neutral-700'
              }`}
            >
              <span className="mr-1">{cat.icon}</span>
              {cat.label}
            </button>
          ))}
        </div>
      )}

      {/* Media Grid/List */}
      {viewMode === 'grid' ? (
        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
          {filteredMedia.map((item, index) => (
            <div
              key={`${item.url}-${index}`}
              className="group relative aspect-square rounded-lg overflow-hidden bg-neutral-200 dark:bg-neutral-700 hover:shadow-lg transition-shadow"
            >
              {/* Media Content */}
              {item.type === 'video' ? (
                <video
                  src={item.url}
                  className="w-full h-full object-cover"
                  preload="metadata"
                />
              ) : (
                <img
                  src={item.url}
                  alt={item.title || item.fileName}
                  className="w-full h-full object-cover cursor-pointer"
                  onClick={() => handleImageClick(index)}
                />
              )}

              {/* Overlay */}
              <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors">
                <div className="absolute top-2 left-2">
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getCategoryColor(item.category)}`}>
                    {getCategoryIcon(item.category)} {item.category}
                  </span>
                </div>

                {item.type === 'video' && (
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="w-12 h-12 rounded-full bg-black/50 flex items-center justify-center">
                      <svg className="w-6 h-6 text-white ml-1" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M8 5v14l11-7z"/>
                      </svg>
                    </div>
                  </div>
                )}

                {canManage && onDelete && (
                  <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                    <button
                      onClick={(e) => {
                        e.stopPropagation()
                        onDelete(item.publicId || item.url)
                      }}
                      className="w-6 h-6 rounded-full bg-red-500 text-white flex items-center justify-center hover:bg-red-600 transition-colors"
                    >
                      ×
                    </button>
                  </div>
                )}
              </div>

              {/* Title */}
              {item.title && (
                <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-2">
                  <p className="text-white text-xs font-medium truncate">
                    {item.title}
                  </p>
                </div>
              )}
            </div>
          ))}
        </div>
      ) : (
        <div className="space-y-3">
          {filteredMedia.map((item, index) => (
            <div
              key={`${item.url}-${index}`}
              className="flex items-center gap-4 p-4 rounded-lg border border-neutral-200 dark:border-neutral-700 hover:bg-neutral-50 dark:hover:bg-neutral-800/50 transition-colors"
            >
              {/* Thumbnail */}
              <div className="w-16 h-16 rounded-lg overflow-hidden bg-neutral-200 dark:bg-neutral-700 flex-shrink-0">
                {item.type === 'video' ? (
                  <video
                    src={item.url}
                    className="w-full h-full object-cover"
                    preload="metadata"
                  />
                ) : (
                  <img
                    src={item.url}
                    alt={item.title || item.fileName}
                    className="w-full h-full object-cover cursor-pointer"
                    onClick={() => handleImageClick(index)}
                  />
                )}
              </div>

              {/* Details */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <h3 className="font-medium text-neutral-900 dark:text-neutral-100 truncate">
                    {item.title || item.fileName}
                  </h3>
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getCategoryColor(item.category)}`}>
                    {getCategoryIcon(item.category)} {item.category}
                  </span>
                </div>
                {item.description && (
                  <p className="text-sm text-neutral-600 dark:text-neutral-400 line-clamp-2">
                    {item.description}
                  </p>
                )}
                {item.tags && item.tags.length > 0 && (
                  <div className="flex flex-wrap gap-1 mt-2">
                    {item.tags.slice(0, 3).map((tag, tagIndex) => (
                      <span
                        key={tagIndex}
                        className="inline-block px-2 py-1 rounded text-xs bg-neutral-100 dark:bg-neutral-700 text-neutral-600 dark:text-neutral-400"
                      >
                        #{tag}
                      </span>
                    ))}
                    {item.tags.length > 3 && (
                      <span className="text-xs text-neutral-500">
                        +{item.tags.length - 3} more
                      </span>
                    )}
                  </div>
                )}
              </div>

              {/* Actions */}
              <div className="flex items-center gap-2">
                <span className="text-xs text-neutral-500 uppercase">
                  {item.type}
                </span>
                {canManage && onDelete && (
                  <button
                    onClick={() => onDelete(item.publicId || item.url)}
                    className="text-red-500 hover:text-red-700 transition-colors"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                  </button>
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Image Preview Modal */}
      <ImagePreviewModal
        isOpen={previewOpen}
        onClose={() => setPreviewOpen(false)}
        images={imageUrls}
        initialIndex={previewIndex}
      />
    </div>
  )
}
