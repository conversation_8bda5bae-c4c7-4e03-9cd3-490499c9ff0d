import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'

interface AcademyTrainingsProps {
  academy: {
    id: number
    name: string
    trainings: Array<{
      id: number
      title: string
      description: string
      type: string
      skillLevel: string
      scheduledAt: Date
      duration: number
      location: string
      maxParticipants: number
      status: string
    }>
  }
}

export function AcademyTrainings({ academy }: AcademyTrainingsProps) {
  const upcomingTrainings = academy.trainings.filter(training => 
    training.status === 'scheduled' || training.status === 'ongoing'
  )

  const getTrainingTypeIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'technical': return '⚽'
      case 'physical': return '💪'
      case 'tactical': return '🧠'
      case 'mental': return '🎯'
      default: return '🏃'
    }
  }

  const getTrainingTypeColor = (type: string) => {
    switch (type.toLowerCase()) {
      case 'technical': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-200'
      case 'physical': return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-200'
      case 'tactical': return 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-200'
      case 'mental': return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-200'
      default: return 'bg-neutral-100 text-neutral-800 dark:bg-neutral-900/30 dark:text-neutral-200'
    }
  }

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <section className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100 md:text-3xl">
          Upcoming Training Sessions
        </h2>
        <p className="mt-2 text-neutral-600 dark:text-neutral-400">
          Join our structured training programs designed to improve your skills
        </p>
      </div>

      {upcomingTrainings.length === 0 ? (
        <Card variant="elevated" className="text-center py-12">
          <CardContent>
            <div className="text-6xl mb-4">📅</div>
            <h3 className="text-xl font-semibold text-neutral-900 dark:text-neutral-100 mb-2">
              No Upcoming Training Sessions
            </h3>
            <p className="text-neutral-600 dark:text-neutral-400 mb-6">
              Training schedules will be posted here. Check back soon!
            </p>
            <Button>Contact Academy</Button>
          </CardContent>
        </Card>
      ) : (
        <>
          <div className="grid gap-6 md:grid-cols-2">
            {upcomingTrainings.map((training) => (
              <Card key={training.id} variant="elevated" className="overflow-hidden">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <CardTitle className="flex items-center space-x-2">
                        <span className="text-2xl">{getTrainingTypeIcon(training.type)}</span>
                        <span className="line-clamp-1">{training.title}</span>
                      </CardTitle>
                      <div className="flex items-center space-x-2 mt-2">
                        <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${getTrainingTypeColor(training.type)}`}>
                          {training.type}
                        </span>
                        <span className="inline-flex items-center rounded-full bg-accent-100 dark:bg-accent-900/30 px-2.5 py-0.5 text-xs font-medium text-accent-800 dark:text-accent-200">
                          {training.skillLevel}
                        </span>
                        <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                          training.status === 'scheduled' 
                            ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-200'
                            : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-200'
                        }`}>
                          {training.status}
                        </span>
                      </div>
                    </div>
                  </div>
                </CardHeader>
                
                <CardContent className="space-y-4">
                  <p className="text-sm text-neutral-600 dark:text-neutral-400 line-clamp-2">
                    {training.description}
                  </p>
                  
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <div className="flex items-center space-x-1 text-neutral-600 dark:text-neutral-400">
                        <span>📅</span>
                        <span>Date & Time</span>
                      </div>
                      <div className="font-medium text-neutral-900 dark:text-neutral-100">
                        {formatDate(training.scheduledAt)}
                      </div>
                    </div>
                    
                    <div>
                      <div className="flex items-center space-x-1 text-neutral-600 dark:text-neutral-400">
                        <span>⏱️</span>
                        <span>Duration</span>
                      </div>
                      <div className="font-medium text-neutral-900 dark:text-neutral-100">
                        {training.duration} minutes
                      </div>
                    </div>
                    
                    <div>
                      <div className="flex items-center space-x-1 text-neutral-600 dark:text-neutral-400">
                        <span>📍</span>
                        <span>Location</span>
                      </div>
                      <div className="font-medium text-neutral-900 dark:text-neutral-100">
                        {training.location}
                      </div>
                    </div>
                    
                    <div>
                      <div className="flex items-center space-x-1 text-neutral-600 dark:text-neutral-400">
                        <span>👥</span>
                        <span>Max Participants</span>
                      </div>
                      <div className="font-medium text-neutral-900 dark:text-neutral-100">
                        {training.maxParticipants}
                      </div>
                    </div>
                  </div>

                  <div className="flex space-x-2">
                    <Button size="sm" className="flex-1">
                      Join Training
                    </Button>
                    <Button size="sm" variant="outline">
                      View Details
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {upcomingTrainings.length > 4 && (
            <div className="text-center">
              <Button variant="outline">
                View All Training Sessions
              </Button>
            </div>
          )}

          {/* Training Schedule Overview */}
          <Card variant="elevated">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <span>📊</span>
                <span>Training Schedule Overview</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary-600 dark:text-primary-400">
                    {upcomingTrainings.filter(t => t.type === 'technical').length}
                  </div>
                  <div className="text-sm text-neutral-600 dark:text-neutral-400">
                    Technical Sessions
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary-600 dark:text-primary-400">
                    {upcomingTrainings.filter(t => t.type === 'physical').length}
                  </div>
                  <div className="text-sm text-neutral-600 dark:text-neutral-400">
                    Physical Training
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary-600 dark:text-primary-400">
                    {upcomingTrainings.filter(t => t.type === 'tactical').length}
                  </div>
                  <div className="text-sm text-neutral-600 dark:text-neutral-400">
                    Tactical Sessions
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary-600 dark:text-primary-400">
                    {Math.round(upcomingTrainings.reduce((sum, t) => sum + t.duration, 0) / 60)}
                  </div>
                  <div className="text-sm text-neutral-600 dark:text-neutral-400">
                    Total Hours
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </>
      )}
    </section>
  )
}
