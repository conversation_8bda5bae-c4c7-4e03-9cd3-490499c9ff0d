'use client'

import { useState } from 'react'
import Link from 'next/link'
import { But<PERSON> } from '@/components/ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'

interface AcademyOnboardingProps {
  className?: string
}

export function AcademyOnboarding({ className = '' }: AcademyOnboardingProps) {
  const [selectedUserType, setSelectedUserType] = useState<string | null>(null)

  const userTypes = [
    {
      id: 'athlete',
      title: 'I\'m an Athlete',
      description: 'Looking to join an academy and develop my skills',
      icon: '🏃‍♂️',
      benefits: [
        'Join top sports academies',
        'Track your performance progress',
        'Upload talent videos and highlights',
        'Get discovered by professional clubs',
        'Access training programs and events'
      ],
      actions: [
        { label: 'Browse Academies', href: '/academies', primary: true },
        { label: 'Learn More', href: '#athlete-info', primary: false }
      ]
    },
    {
      id: 'academy',
      title: 'I\'m an Academy Owner',
      description: 'Want to register my academy and manage members',
      icon: '🏫',
      benefits: [
        'Create your academy profile',
        'Manage member registrations',
        'Track member performance',
        'Organize training sessions and events',
        'Connect with clubs and sponsors'
      ],
      actions: [
        { label: 'Register Academy', href: '/academies/register', primary: true },
        { label: 'View Demo', href: '#academy-demo', primary: false }
      ]
    },
    {
      id: 'scout',
      title: 'I\'m a Scout/Club Representative',
      description: 'Looking to discover and recruit talented athletes',
      icon: '🔍',
      benefits: [
        'Browse academy member profiles',
        'View talent videos and performance data',
        'Contact academies for partnerships',
        'Track promising athletes',
        'Access detailed analytics'
      ],
      actions: [
        { label: 'Start Scouting', href: '/academies', primary: true },
        { label: 'Partnership Info', href: '#partnership-info', primary: false }
      ]
    }
  ]

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100 mb-2">
          Welcome to Sports Academies
        </h2>
        <p className="text-neutral-600 dark:text-neutral-400 max-w-2xl mx-auto">
          Connect talented athletes with world-class training programs and professional opportunities. 
          Choose your role to get started.
        </p>
      </div>

      {/* User Type Selection */}
      <div className="grid gap-6 md:grid-cols-3">
        {userTypes.map((userType) => (
          <Card 
            key={userType.id}
            className={`cursor-pointer transition-all duration-200 hover:shadow-lg ${
              selectedUserType === userType.id 
                ? 'ring-2 ring-primary-500 border-primary-300 dark:border-primary-600' 
                : 'hover:border-primary-200 dark:hover:border-primary-700'
            }`}
            onClick={() => setSelectedUserType(selectedUserType === userType.id ? null : userType.id)}
          >
            <CardHeader className="text-center">
              <div className="text-4xl mb-2">{userType.icon}</div>
              <CardTitle className="text-lg">{userType.title}</CardTitle>
              <p className="text-sm text-neutral-600 dark:text-neutral-400">
                {userType.description}
              </p>
            </CardHeader>
            
            {selectedUserType === userType.id && (
              <CardContent className="pt-0">
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium text-neutral-900 dark:text-neutral-100 mb-2">
                      What you can do:
                    </h4>
                    <ul className="space-y-1">
                      {userType.benefits.map((benefit, index) => (
                        <li key={index} className="flex items-center text-sm text-neutral-600 dark:text-neutral-400">
                          <svg className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                          {benefit}
                        </li>
                      ))}
                    </ul>
                  </div>
                  
                  <div className="flex flex-col space-y-2">
                    {userType.actions.map((action, index) => (
                      <Button
                        key={index}
                        variant={action.primary ? 'primary' : 'outline'}
                        size="sm"
                        className="w-full"
                        asChild
                      >
                        <Link href={action.href}>
                          {action.label}
                        </Link>
                      </Button>
                    ))}
                  </div>
                </div>
              </CardContent>
            )}
          </Card>
        ))}
      </div>

      {/* Quick Stats */}
      <div className="bg-neutral-50 dark:bg-neutral-800 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-neutral-900 dark:text-neutral-100 mb-4 text-center">
          Join Our Growing Community
        </h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
          <div>
            <div className="text-2xl font-bold text-primary-600 dark:text-primary-400">50+</div>
            <div className="text-sm text-neutral-600 dark:text-neutral-400">Academies</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-primary-600 dark:text-primary-400">1,200+</div>
            <div className="text-sm text-neutral-600 dark:text-neutral-400">Athletes</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-primary-600 dark:text-primary-400">25+</div>
            <div className="text-sm text-neutral-600 dark:text-neutral-400">Sports</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-primary-600 dark:text-primary-400">100+</div>
            <div className="text-sm text-neutral-600 dark:text-neutral-400">Success Stories</div>
          </div>
        </div>
      </div>

      {/* Call to Action */}
      {!selectedUserType && (
        <div className="text-center">
          <p className="text-neutral-600 dark:text-neutral-400 mb-4">
            Not sure where to start? Click on any card above to learn more about what you can do.
          </p>
          <Button variant="outline" asChild>
            <Link href="/academies">
              Browse All Academies
            </Link>
          </Button>
        </div>
      )}
    </div>
  )
}
