import { Button } from '@/components/ui/Button'

interface AcademyHeroProps {
  academy: {
    id: number
    name: string
    sport: string
    level: string
    logo?: string | null
    location: string
    _count?: {
      members: number
      trainings: number
      events: number
    }
  }
  content: any
}

export function AcademyHero({ academy, content }: AcademyHeroProps) {
  // Define brand colors for different sports
  const getSportColors = (sport: string) => {
    const sportLower = sport.toLowerCase()

    if (sportLower.includes('football') || sportLower.includes('soccer')) {
      return 'bg-gradient-to-r from-green-600 to-green-800' // Football green
    } else if (sportLower.includes('basketball')) {
      return 'bg-gradient-to-r from-orange-600 to-red-600' // Basketball orange/red
    } else if (sportLower.includes('volleyball')) {
      return 'bg-gradient-to-r from-blue-600 to-cyan-600' // Volleyball blue
    } else if (sportLower.includes('athletics') || sportLower.includes('track')) {
      return 'bg-gradient-to-r from-purple-600 to-pink-600' // Athletics purple
    } else if (sportLower.includes('tennis')) {
      return 'bg-gradient-to-r from-yellow-600 to-green-600' // Tennis yellow/green
    } else {
      return 'bg-gradient-to-r from-primary-600 to-accent-600' // Default
    }
  }

  const sportColors = getSportColors(academy.sport)

  return (
    <div className={`relative py-20 ${sportColors}`}>
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-black/20"></div>
      
      <div className="container-mobile relative">
        <div className="flex flex-col items-center text-center text-white md:flex-row md:text-left">
          {/* Academy Logo */}
          <div className="mb-6 md:mb-0 md:mr-8">
            <div className="mx-auto h-24 w-24 overflow-hidden rounded-full bg-white p-2 shadow-lg md:h-32 md:w-32">
              {academy.logo ? (
                <img
                  src={academy.logo}
                  alt={`${academy.name} logo`}
                  className="h-full w-full object-contain"
                />
              ) : (
                <div className="flex h-full w-full items-center justify-center bg-neutral-100 text-2xl font-bold text-neutral-600 md:text-3xl">
                  {academy.name.charAt(0)}
                </div>
              )}
            </div>
          </div>

          {/* Academy Info */}
          <div className="flex-1">
            <div className="mb-2 flex items-center justify-center space-x-2 md:justify-start">
              <span className="rounded-full bg-white/20 px-3 py-1 text-sm font-medium">
                {academy.sport}
              </span>
              <span className="rounded-full bg-white/20 px-3 py-1 text-sm font-medium">
                {academy.level}
              </span>
              {academy._count && (
                <span className="rounded-full bg-white/20 px-3 py-1 text-sm font-medium">
                  {academy._count.members} Members
                </span>
              )}
            </div>
            
            <h1 className="mb-4 text-3xl font-bold md:text-5xl">
              {academy.name}
            </h1>
            
            <p className="mb-4 text-lg text-white/90 md:text-xl">
              {content?.tagline || `Premier ${academy.sport} academy in ${academy.location}`}
            </p>

            <div className="mb-6 flex items-center justify-center space-x-2 text-white/80 md:justify-start">
              <span>📍</span>
              <span>{academy.location}</span>
            </div>

            {content?.founded && (
              <p className="mb-6 text-white/80">
                Established: {content.founded}
              </p>
            )}

            {/* Action Buttons */}
            <div className="flex flex-col gap-3 sm:flex-row">
              <Button size="lg" variant="secondary">
                Join Academy
              </Button>
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-neutral-900">
                View Programs
              </Button>
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-neutral-900">
                Contact Us
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Stats */}
      {academy._count && (
        <div className="container-mobile mt-12">
          <div className="grid grid-cols-3 gap-4 rounded-lg bg-white/10 p-6 backdrop-blur-sm">
            <div className="text-center">
              <div className="text-2xl font-bold text-white md:text-3xl">
                {academy._count.members}
              </div>
              <div className="text-sm text-white/80">
                Active Members
              </div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-white md:text-3xl">
                {academy._count.trainings}
              </div>
              <div className="text-sm text-white/80">
                Training Sessions
              </div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-white md:text-3xl">
                {academy._count.events}
              </div>
              <div className="text-sm text-white/80">
                Upcoming Events
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
