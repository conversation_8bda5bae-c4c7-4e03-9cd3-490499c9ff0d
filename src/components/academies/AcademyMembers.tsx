import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'

interface AcademyMembersProps {
  academy: {
    id: number
    name: string
    members: Array<{
      id: number
      status: string
      position?: string | null
      skillLevel: string
      user: {
        id: number
        firstName: string
        lastName: string
        avatar?: string | null
      }
    }>
  }
}

export function AcademyMembers({ academy }: AcademyMembersProps) {
  const activeMembers = academy.members.filter(member => member.status === 'active')

  return (
    <section className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100 md:text-3xl">
          Our Members
        </h2>
        <p className="mt-2 text-neutral-600 dark:text-neutral-400">
          Meet the talented athletes training at {academy.name}
        </p>
      </div>

      {activeMembers.length === 0 ? (
        <Card variant="elevated" className="text-center py-12">
          <CardContent>
            <div className="text-6xl mb-4">👥</div>
            <h3 className="text-xl font-semibold text-neutral-900 dark:text-neutral-100 mb-2">
              No Active Members Yet
            </h3>
            <p className="text-neutral-600 dark:text-neutral-400 mb-6">
              Be the first to join this academy and start your training journey
            </p>
            <Button>Join Academy</Button>
          </CardContent>
        </Card>
      ) : (
        <>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
            {activeMembers.slice(0, 8).map((member) => (
              <Card key={member.id} variant="elevated" className="text-center">
                <CardHeader className="pb-2">
                  <div className="mx-auto h-16 w-16 overflow-hidden rounded-full bg-neutral-100 dark:bg-neutral-800">
                    {member.user.avatar ? (
                      <img
                        src={member.user.avatar}
                        alt={`${member.user.firstName} ${member.user.lastName}`}
                        className="h-full w-full object-cover"
                      />
                    ) : (
                      <div className="flex h-full w-full items-center justify-center bg-primary-500 text-white font-bold text-lg">
                        {member.user.firstName[0]}{member.user.lastName[0]}
                      </div>
                    )}
                  </div>
                </CardHeader>
                <CardContent className="space-y-2">
                  <h3 className="font-semibold text-neutral-900 dark:text-neutral-100">
                    {member.user.firstName} {member.user.lastName}
                  </h3>
                  
                  <div className="space-y-1">
                    {member.position && (
                      <span className="inline-flex items-center rounded-full bg-primary-100 dark:bg-primary-900/30 px-2.5 py-0.5 text-xs font-medium text-primary-800 dark:text-primary-200">
                        {member.position}
                      </span>
                    )}
                    <div>
                      <span className="inline-flex items-center rounded-full bg-accent-100 dark:bg-accent-900/30 px-2.5 py-0.5 text-xs font-medium text-accent-800 dark:text-accent-200">
                        {member.skillLevel}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {activeMembers.length > 8 && (
            <div className="text-center">
              <Button variant="outline">
                View All {activeMembers.length} Members
              </Button>
            </div>
          )}

          {/* Member Statistics */}
          <div className="grid gap-4 md:grid-cols-4">
            <Card variant="elevated" className="text-center">
              <CardContent className="py-4">
                <div className="text-2xl font-bold text-primary-600 dark:text-primary-400">
                  {activeMembers.length}
                </div>
                <div className="text-sm text-neutral-600 dark:text-neutral-400">
                  Active Members
                </div>
              </CardContent>
            </Card>

            <Card variant="elevated" className="text-center">
              <CardContent className="py-4">
                <div className="text-2xl font-bold text-primary-600 dark:text-primary-400">
                  {activeMembers.filter(m => m.skillLevel === 'elite').length}
                </div>
                <div className="text-sm text-neutral-600 dark:text-neutral-400">
                  Elite Level
                </div>
              </CardContent>
            </Card>

            <Card variant="elevated" className="text-center">
              <CardContent className="py-4">
                <div className="text-2xl font-bold text-primary-600 dark:text-primary-400">
                  {activeMembers.filter(m => m.skillLevel === 'advanced').length}
                </div>
                <div className="text-sm text-neutral-600 dark:text-neutral-400">
                  Advanced Level
                </div>
              </CardContent>
            </Card>

            <Card variant="elevated" className="text-center">
              <CardContent className="py-4">
                <div className="text-2xl font-bold text-primary-600 dark:text-primary-400">
                  {new Set(activeMembers.map(m => m.position).filter(Boolean)).size}
                </div>
                <div className="text-sm text-neutral-600 dark:text-neutral-400">
                  Positions Covered
                </div>
              </CardContent>
            </Card>
          </div>
        </>
      )}
    </section>
  )
}
