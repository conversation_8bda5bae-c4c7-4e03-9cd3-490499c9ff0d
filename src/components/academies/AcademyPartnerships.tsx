'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { getAcademyPartnerships, getAvailablePartners, createAcademyPartnership, updatePartnershipStatus } from '@/app/actions/academies'

interface Partnership {
  id: number
  partnerType: string
  partnerId: number
  partnershipType: string
  status: string
  terms: any
  startDate: Date
  endDate?: Date | null
  createdAt: Date
  updatedAt: Date
  academyId: number
  partnerDetails?: {
    name: string
    logo?: string | null
    sport?: string
    industry?: string
    level?: string
  } | null
}

interface Partner {
  id: number
  name: string
  logo?: string | null
  sport?: string
  industry?: string
  level?: string
}

interface AcademyPartnershipsProps {
  academyId: number
  canManage?: boolean
}

export function AcademyPartnerships({ academyId, canManage = false }: AcademyPartnershipsProps) {
  const [partnerships, setPartnerships] = useState<Partnership[]>([])
  const [availablePartners, setAvailablePartners] = useState<Partner[]>([])
  const [showAddForm, setShowAddForm] = useState(false)
  const [loading, setLoading] = useState(true)
  const [formData, setFormData] = useState({
    partnerType: 'club' as 'club' | 'company' | 'provider' | 'institution',
    partnerId: 0,
    partnershipType: 'scouting' as 'scouting' | 'sponsorship' | 'equipment' | 'facility' | 'training' | 'scholarship',
    description: '',
    benefits: [''],
    responsibilities: [''],
    financialTerms: '',
    duration: '',
    startDate: new Date().toISOString().split('T')[0],
    endDate: ''
  })

  useEffect(() => {
    loadPartnerships()
  }, [academyId])

  useEffect(() => {
    if (showAddForm) {
      loadAvailablePartners(formData.partnerType)
    }
  }, [formData.partnerType, showAddForm])

  const loadPartnerships = async () => {
    setLoading(true)
    const result = await getAcademyPartnerships(academyId)
    if (result.success) {
      setPartnerships(result.data)
    }
    setLoading(false)
  }

  const loadAvailablePartners = async (partnerType: 'club' | 'company' | 'provider' | 'institution') => {
    const result = await getAvailablePartners(partnerType)
    if (result.success) {
      setAvailablePartners(result.data)
    }
  }

  const handleCreatePartnership = async (e: React.FormEvent) => {
    e.preventDefault()

    const partnershipData = {
      academyId,
      partnerId: formData.partnerId,
      partnerType: formData.partnerType,
      partnershipType: formData.partnershipType,
      terms: {
        description: formData.description,
        benefits: formData.benefits.filter(b => b.trim()),
        responsibilities: formData.responsibilities.filter(r => r.trim()),
        financialTerms: formData.financialTerms,
        duration: formData.duration
      },
      startDate: new Date(formData.startDate),
      endDate: formData.endDate ? new Date(formData.endDate) : undefined
    }

    const result = await createAcademyPartnership(partnershipData)
    if (result.success) {
      setShowAddForm(false)
      loadPartnerships()
      // Reset form
      setFormData({
        partnerType: 'club',
        partnerId: 0,
        partnershipType: 'scouting',
        description: '',
        benefits: [''],
        responsibilities: [''],
        financialTerms: '',
        duration: '',
        startDate: new Date().toISOString().split('T')[0],
        endDate: ''
      })
    }
  }

  const handleStatusUpdate = async (partnershipId: number, status: 'active' | 'inactive' | 'pending' | 'expired') => {
    const result = await updatePartnershipStatus(partnershipId, status)
    if (result.success) {
      loadPartnerships()
    }
  }

  const partnershipTypes = {
    club: [
      'scouting',
      'training',
      'facility'
    ],
    company: [
      'sponsorship',
      'equipment',
      'scholarship'
    ],
    provider: [
      'equipment',
      'facility'
    ],
    institution: [
      'training',
      'scholarship'
    ]
  }

  const addBenefit = () => {
    setFormData(prev => ({
      ...prev,
      benefits: [...prev.benefits, '']
    }))
  }

  const addResponsibility = () => {
    setFormData(prev => ({
      ...prev,
      responsibilities: [...prev.responsibilities, '']
    }))
  }

  const updateBenefit = (index: number, value: string) => {
    setFormData(prev => ({
      ...prev,
      benefits: prev.benefits.map((b, i) => i === index ? value : b)
    }))
  }

  const updateResponsibility = (index: number, value: string) => {
    setFormData(prev => ({
      ...prev,
      responsibilities: prev.responsibilities.map((r, i) => i === index ? value : r)
    }))
  }

  const removeBenefit = (index: number) => {
    setFormData(prev => ({
      ...prev,
      benefits: prev.benefits.filter((_, i) => i !== index)
    }))
  }

  const removeResponsibility = (index: number) => {
    setFormData(prev => ({
      ...prev,
      responsibilities: prev.responsibilities.filter((_, i) => i !== index)
    }))
  }

  const getPartnershipIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'scouting': return '🔍'
      case 'training': return '🏃‍♂️'
      case 'facility': return '🏟️'
      case 'sponsorship': return '💰'
      case 'equipment': return '⚽'
      case 'scholarship': return '🎓'
      default: return '🤝'
    }
  }

  const getPartnerTypeIcon = (type: string) => {
    switch (type) {
      case 'club': return '⚽'
      case 'company': return '🏢'
      case 'provider': return '📱'
      case 'institution': return '🏫'
      default: return '🤝'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      case 'pending': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
      case 'inactive': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
      case 'expired': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    }
  }

  return (
    <section className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100">
            Partnerships
          </h2>
          <p className="text-neutral-600 dark:text-neutral-400">
            Collaborations with clubs and companies
          </p>
        </div>
        {canManage && (
          <Button onClick={() => setShowAddForm(true)}>
            Add Partnership
          </Button>
        )}
      </div>

      {loading ? (
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
          <p className="text-neutral-600 dark:text-neutral-400 mt-2">Loading partnerships...</p>
        </div>
      ) : partnerships.length > 0 ? (
        <div className="grid gap-6 md:grid-cols-2">
          {partnerships.map((partnership) => (
            <Card key={partnership.id} variant="elevated">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="text-2xl">
                      {getPartnershipIcon(partnership.partnershipType)}
                    </div>
                    <div>
                      <CardTitle className="text-lg">
                        {partnership.partnerDetails?.name || `${partnership.partnerType} Partner`}
                      </CardTitle>
                      <p className="text-sm text-neutral-600 dark:text-neutral-400">
                        {partnership.partnershipType.charAt(0).toUpperCase() + partnership.partnershipType.slice(1)} Partnership
                      </p>
                      <div className="flex items-center space-x-2 mt-1">
                        <span className="text-xs text-neutral-500">
                          {getPartnerTypeIcon(partnership.partnerType)} {partnership.partnerType.charAt(0).toUpperCase() + partnership.partnerType.slice(1)}
                        </span>
                        {partnership.partnerDetails?.sport && (
                          <span className="text-xs text-neutral-500">• {partnership.partnerDetails.sport}</span>
                        )}
                        {partnership.partnerDetails?.industry && (
                          <span className="text-xs text-neutral-500">• {partnership.partnerDetails.industry}</span>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="flex flex-col items-end space-y-2">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(partnership.status)}`}>
                      {partnership.status.charAt(0).toUpperCase() + partnership.status.slice(1)}
                    </span>
                    {canManage && (
                      <select
                        value={partnership.status}
                        onChange={(e) => handleStatusUpdate(partnership.id, e.target.value as any)}
                        className="text-xs border border-neutral-300 dark:border-neutral-600 rounded px-2 py-1 bg-white dark:bg-neutral-800"
                      >
                        <option value="pending">Pending</option>
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                        <option value="expired">Expired</option>
                      </select>
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {partnership.terms?.description && (
                    <div>
                      <h4 className="font-medium text-neutral-900 dark:text-neutral-100 mb-2">
                        Description
                      </h4>
                      <p className="text-sm text-neutral-600 dark:text-neutral-400">
                        {partnership.terms.description}
                      </p>
                    </div>
                  )}

                  {partnership.terms?.benefits && Array.isArray(partnership.terms.benefits) && partnership.terms.benefits.length > 0 && (
                    <div>
                      <h4 className="font-medium text-neutral-900 dark:text-neutral-100 mb-2">
                        Benefits
                      </h4>
                      <ul className="text-sm text-neutral-600 dark:text-neutral-400 space-y-1">
                        {partnership.terms.benefits.map((benefit: string, index: number) => (
                          <li key={index} className="flex items-start">
                            <span className="text-green-500 mr-2">•</span>
                            {benefit}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {partnership.terms?.responsibilities && Array.isArray(partnership.terms.responsibilities) && partnership.terms.responsibilities.length > 0 && (
                    <div>
                      <h4 className="font-medium text-neutral-900 dark:text-neutral-100 mb-2">
                        Responsibilities
                      </h4>
                      <ul className="text-sm text-neutral-600 dark:text-neutral-400 space-y-1">
                        {partnership.terms.responsibilities.map((responsibility: string, index: number) => (
                          <li key={index} className="flex items-start">
                            <span className="text-blue-500 mr-2">•</span>
                            {responsibility}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                  <div className="flex items-center justify-between text-sm text-neutral-500">
                    <span>
                      Started: {new Date(partnership.startDate).toLocaleDateString()}
                    </span>
                    {partnership.endDate && (
                      <span>
                        Ends: {new Date(partnership.endDate).toLocaleDateString()}
                      </span>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <Card>
          <CardContent className="text-center py-12">
            <div className="text-4xl mb-4">🤝</div>
            <h3 className="text-lg font-medium text-neutral-900 dark:text-neutral-100 mb-2">
              No Partnerships Yet
            </h3>
            <p className="text-neutral-600 dark:text-neutral-400 mb-6">
              Start building relationships with clubs and companies to enhance your academy.
            </p>
            {canManage && (
              <Button onClick={() => setShowAddForm(true)}>
                Create First Partnership
              </Button>
            )}
          </CardContent>
        </Card>
      )}

      {/* Add Partnership Modal/Form */}
      {showAddForm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <Card className="w-full max-w-3xl max-h-[90vh] overflow-y-auto">
            <CardHeader>
              <CardTitle>Add New Partnership</CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleCreatePartnership} className="space-y-6">
                <div className="grid gap-4 md:grid-cols-3">
                  <div>
                    <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2">
                      Partner Type
                    </label>
                    <select
                      value={formData.partnerType}
                      onChange={(e) => setFormData({ ...formData, partnerType: e.target.value as any, partnerId: 0 })}
                      className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100"
                    >
                      <option value="club">Football Club</option>
                      <option value="company">Company</option>
                      <option value="provider">Provider</option>
                      <option value="institution">Institution</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2">
                      Partnership Type
                    </label>
                    <select
                      value={formData.partnershipType}
                      onChange={(e) => setFormData({ ...formData, partnershipType: e.target.value as 'scouting' | 'sponsorship' | 'equipment' | 'facility' | 'training' | 'scholarship' })}
                      className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100"
                    >
                      {partnershipTypes[formData.partnerType].map((type) => (
                        <option key={type} value={type}>{type.charAt(0).toUpperCase() + type.slice(1)}</option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2">
                      Select Partner
                    </label>
                    <select
                      value={formData.partnerId}
                      onChange={(e) => setFormData({ ...formData, partnerId: parseInt(e.target.value) })}
                      className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100"
                      required
                    >
                      <option value={0}>Select partner...</option>
                      {availablePartners.map((partner) => (
                        <option key={partner.id} value={partner.id}>
                          {partner.name} {partner.sport && `(${partner.sport})`} {partner.industry && `(${partner.industry})`}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2">
                      Start Date
                    </label>
                    <input
                      type="date"
                      value={formData.startDate}
                      onChange={(e) => setFormData({ ...formData, startDate: e.target.value })}
                      className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2">
                      End Date (Optional)
                    </label>
                    <input
                      type="date"
                      value={formData.endDate}
                      onChange={(e) => setFormData({ ...formData, endDate: e.target.value })}
                      className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2">
                    Description
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100"
                    rows={3}
                    placeholder="Describe the partnership..."
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2">
                    Benefits
                  </label>
                  {formData.benefits.map((benefit, index) => (
                    <div key={index} className="flex items-center space-x-2 mb-2">
                      <input
                        type="text"
                        value={benefit}
                        onChange={(e) => updateBenefit(index, e.target.value)}
                        className="flex-1 px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100"
                        placeholder="Enter benefit..."
                      />
                      {formData.benefits.length > 1 && (
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => removeBenefit(index)}
                        >
                          Remove
                        </Button>
                      )}
                    </div>
                  ))}
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={addBenefit}
                  >
                    Add Benefit
                  </Button>
                </div>

                <div>
                  <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2">
                    Responsibilities
                  </label>
                  {formData.responsibilities.map((responsibility, index) => (
                    <div key={index} className="flex items-center space-x-2 mb-2">
                      <input
                        type="text"
                        value={responsibility}
                        onChange={(e) => updateResponsibility(index, e.target.value)}
                        className="flex-1 px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100"
                        placeholder="Enter responsibility..."
                      />
                      {formData.responsibilities.length > 1 && (
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => removeResponsibility(index)}
                        >
                          Remove
                        </Button>
                      )}
                    </div>
                  ))}
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={addResponsibility}
                  >
                    Add Responsibility
                  </Button>
                </div>

                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2">
                      Financial Terms (Optional)
                    </label>
                    <input
                      type="text"
                      value={formData.financialTerms}
                      onChange={(e) => setFormData({ ...formData, financialTerms: e.target.value })}
                      className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100"
                      placeholder="e.g., $5000/month sponsorship"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2">
                      Duration (Optional)
                    </label>
                    <input
                      type="text"
                      value={formData.duration}
                      onChange={(e) => setFormData({ ...formData, duration: e.target.value })}
                      className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100"
                      placeholder="e.g., 2 years, 1 season"
                    />
                  </div>
                </div>

                <div className="flex space-x-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setShowAddForm(false)}
                    className="flex-1"
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    className="flex-1"
                    disabled={!formData.partnerId || !formData.description}
                  >
                    Create Partnership
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>
      )}
    </section>
  )
}
