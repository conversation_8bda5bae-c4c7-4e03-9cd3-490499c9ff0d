import Link from 'next/link'
import Image from 'next/image'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { getInternationalNetworks, getDiplomaticEvents, getCulturalExchanges } from '@/app/actions/networking'

export default async function NetworkingPage() {
  const [
    { networks },
    { events },
    { exchanges }
  ] = await Promise.all([
    getInternationalNetworks(),
    getDiplomaticEvents(),
    getCulturalExchanges()
  ])

  const getCountryFlag = (countryCode: string) => {
    // Simple country code to flag emoji mapping
    const flags: { [key: string]: string } = {
      'TZ': '🇹🇿', 'KE': '🇰🇪', 'UG': '🇺🇬', 'RW': '🇷🇼', 'BI': '🇧🇮',
      'US': '🇺🇸', 'GB': '🇬🇧', 'DE': '🇩🇪', 'FR': '🇫🇷', 'BR': '🇧🇷'
    }
    return flags[countryCode] || '🌍'
  }

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    }).format(new Date(date))
  }

  return (
    <div className="min-h-screen bg-neutral-50 dark:bg-neutral-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-neutral-900 dark:text-neutral-100 mb-4">
            Networking & Diplomatic Bridges
          </h1>
          <p className="text-xl text-neutral-600 dark:text-neutral-400 max-w-3xl mx-auto">
            Build international connections, foster diplomatic relationships, and create 
            cultural exchange opportunities through the universal language of football and sports.
          </p>
        </div>

        {/* Key Features */}
        <div className="grid gap-6 md:grid-cols-4 mb-12">
          <Card className="text-center">
            <CardHeader>
              <div className="w-16 h-16 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🌍</span>
              </div>
              <CardTitle>Global Networks</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-neutral-600 dark:text-neutral-400">
                Connect with sports professionals, diplomats, and organizations worldwide.
              </p>
            </CardContent>
          </Card>

          <Card className="text-center">
            <CardHeader>
              <div className="w-16 h-16 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🤝</span>
              </div>
              <CardTitle>Diplomatic Events</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-neutral-600 dark:text-neutral-400">
                Participate in summits, conferences, and diplomatic sports events.
              </p>
            </CardContent>
          </Card>

          <Card className="text-center">
            <CardHeader>
              <div className="w-16 h-16 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🎭</span>
              </div>
              <CardTitle>Cultural Exchange</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-neutral-600 dark:text-neutral-400">
                Experience different cultures through sports exchange programs.
              </p>
            </CardContent>
          </Card>

          <Card className="text-center">
            <CardHeader>
              <div className="w-16 h-16 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🌉</span>
              </div>
              <CardTitle>Bridge Building</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-neutral-600 dark:text-neutral-400">
                Foster understanding and cooperation between nations through sports.
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Network Types */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100 mb-6">
            Network Types
          </h2>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card className="hover:shadow-lg transition-shadow cursor-pointer">
              <CardContent className="p-6 text-center">
                <div className="text-3xl mb-2">🏛️</div>
                <h3 className="font-semibold mb-2">Diplomatic</h3>
                <p className="text-sm text-neutral-600 dark:text-neutral-400">
                  Government officials, ambassadors, diplomatic corps
                </p>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-shadow cursor-pointer">
              <CardContent className="p-6 text-center">
                <div className="text-3xl mb-2">💼</div>
                <h3 className="font-semibold mb-2">Business</h3>
                <p className="text-sm text-neutral-600 dark:text-neutral-400">
                  International business leaders, investors, entrepreneurs
                </p>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-shadow cursor-pointer">
              <CardContent className="p-6 text-center">
                <div className="text-3xl mb-2">⚽</div>
                <h3 className="font-semibold mb-2">Sports</h3>
                <p className="text-sm text-neutral-600 dark:text-neutral-400">
                  Athletes, coaches, sports administrators, federations
                </p>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-shadow cursor-pointer">
              <CardContent className="p-6 text-center">
                <div className="text-3xl mb-2">🎓</div>
                <h3 className="font-semibold mb-2">Academic</h3>
                <p className="text-sm text-neutral-600 dark:text-neutral-400">
                  Researchers, educators, academic institutions
                </p>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Active Networks */}
        <div className="mb-12">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100">
              Active International Networks
            </h2>
            <Button asChild>
              <Link href="/networking/create">
                Create Network
              </Link>
            </Button>
          </div>

          {networks && networks.length > 0 ? (
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {networks.map((network) => (
                <Card key={network.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <CardTitle className="text-lg mb-2">{network.title}</CardTitle>
                        <div className="flex items-center space-x-2 text-sm mb-2">
                          <span className="bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300 px-2 py-1 rounded">
                            {network.networkType}
                          </span>
                          <span className="bg-neutral-100 dark:bg-neutral-800 px-2 py-1 rounded">
                            {network.membershipType}
                          </span>
                        </div>
                        <div className="flex items-center space-x-1 mb-2">
                          {network.countries.slice(0, 5).map((country, index) => (
                            <span key={index} className="text-lg">
                              {getCountryFlag(country)}
                            </span>
                          ))}
                          {network.countries.length > 5 && (
                            <span className="text-sm text-neutral-500">
                              +{network.countries.length - 5} more
                            </span>
                          )}
                        </div>
                      </div>
                      {network.creator.avatar && (
                        <Image
                          src={network.creator.avatar}
                          alt={`${network.creator.firstName} ${network.creator.lastName}`}
                          width={40}
                          height={40}
                          className="rounded-full"
                        />
                      )}
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-neutral-600 dark:text-neutral-400 mb-4 line-clamp-3">
                      {network.description}
                    </p>
                    
                    <div className="space-y-2 mb-4">
                      <div className="text-sm">
                        <span className="text-neutral-500">Objectives:</span>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {network.objectives.slice(0, 3).map((objective, index) => (
                            <span
                              key={index}
                              className="text-xs bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 px-2 py-1 rounded"
                            >
                              {objective}
                            </span>
                          ))}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center justify-between text-sm text-neutral-500 mb-4">
                      <span>{network._count.memberships} members</span>
                      <span>{network._count.events} events</span>
                      <span>{network._count.exchanges} exchanges</span>
                    </div>

                    <div className="flex space-x-2">
                      <Button size="sm" className="flex-1" asChild>
                        <Link href={`/networking/networks/${network.id}`}>
                          View Network
                        </Link>
                      </Button>
                      <Button size="sm" variant="outline" asChild>
                        <Link href={`/networking/networks/${network.id}/join`}>
                          Join
                        </Link>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="p-12 text-center">
                <div className="text-6xl mb-4">🌍</div>
                <h3 className="text-xl font-semibold mb-2">No Networks Available</h3>
                <p className="text-neutral-600 dark:text-neutral-400 mb-6">
                  Be the first to create an international network for sports diplomacy.
                </p>
                <Button asChild>
                  <Link href="/networking/create">
                    Create First Network
                  </Link>
                </Button>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Upcoming Events */}
        <div className="mb-12">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100">
              Upcoming Diplomatic Events
            </h2>
            <Button variant="outline" asChild>
              <Link href="/networking/events">
                View All Events
              </Link>
            </Button>
          </div>

          {events && events.length > 0 ? (
            <div className="grid gap-4 md:grid-cols-2">
              {events.slice(0, 4).map((event) => (
                <Card key={event.id} className="hover:shadow-lg transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <h3 className="font-semibold text-lg mb-2">{event.title}</h3>
                        <div className="flex items-center space-x-2 text-sm mb-2">
                          <span className="bg-accent-100 dark:bg-accent-900 text-accent-700 dark:text-accent-300 px-2 py-1 rounded">
                            {event.type}
                          </span>
                          <span className="text-neutral-500">
                            {formatDate(event.startDate)} - {formatDate(event.endDate)}
                          </span>
                        </div>
                        <div className="flex items-center space-x-1 mb-2">
                          {event.countries.slice(0, 4).map((country, index) => (
                            <span key={index} className="text-lg">
                              {getCountryFlag(country)}
                            </span>
                          ))}
                        </div>
                      </div>
                    </div>
                    <p className="text-neutral-600 dark:text-neutral-400 mb-4 line-clamp-2">
                      {event.description}
                    </p>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-neutral-500">{event.location}</span>
                      <Button size="sm" asChild>
                        <Link href={`/networking/events/${event.id}`}>
                          Learn More
                        </Link>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="p-8 text-center">
                <div className="text-4xl mb-2">📅</div>
                <p className="text-neutral-600 dark:text-neutral-400">
                  No upcoming diplomatic events. Check back soon for new opportunities.
                </p>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Cultural Exchanges */}
        <div className="mb-12">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100">
              Cultural Exchange Programs
            </h2>
            <Button variant="outline" asChild>
              <Link href="/networking/exchanges">
                View All Programs
              </Link>
            </Button>
          </div>

          {exchanges && exchanges.length > 0 ? (
            <div className="grid gap-4 md:grid-cols-2">
              {exchanges.slice(0, 4).map((exchange) => (
                <Card key={exchange.id} className="hover:shadow-lg transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <h3 className="font-semibold text-lg mb-2">{exchange.title}</h3>
                        <div className="flex items-center space-x-2 text-sm mb-2">
                          <span className="bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300 px-2 py-1 rounded">
                            {exchange.type}
                          </span>
                          <span className="text-neutral-500">
                            {exchange.duration} days
                          </span>
                        </div>
                        <div className="flex items-center space-x-2 mb-2">
                          <span className="text-lg">{getCountryFlag(exchange.sourceCountry)}</span>
                          <span className="text-neutral-400">→</span>
                          <span className="text-lg">{getCountryFlag(exchange.targetCountry)}</span>
                        </div>
                      </div>
                    </div>
                    <p className="text-neutral-600 dark:text-neutral-400 mb-4 line-clamp-2">
                      {exchange.description}
                    </p>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-neutral-500">
                        {exchange._count.applications} applications
                      </span>
                      <Button size="sm" asChild>
                        <Link href={`/networking/exchanges/${exchange.id}`}>
                          Apply Now
                        </Link>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="p-8 text-center">
                <div className="text-4xl mb-2">🎭</div>
                <p className="text-neutral-600 dark:text-neutral-400">
                  No active exchange programs. New opportunities coming soon.
                </p>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Success Stories */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100 mb-6">
            Diplomatic Success Stories
          </h2>
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-4 mb-4">
                  <div className="w-12 h-12 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center">
                    <span className="text-xl">🤝</span>
                  </div>
                  <div>
                    <h4 className="font-semibold">East Africa Sports Summit</h4>
                    <p className="text-sm text-neutral-600 dark:text-neutral-400">
                      5 countries, 200+ delegates
                    </p>
                  </div>
                </div>
                <p className="text-neutral-600 dark:text-neutral-400">
                  "The summit led to the signing of a historic sports cooperation agreement 
                  between Tanzania, Kenya, Uganda, Rwanda, and Burundi, promoting youth 
                  development through football."
                </p>
                <div className="mt-4 text-sm text-neutral-500">
                  - East African Community Sports Council
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-4 mb-4">
                  <div className="w-12 h-12 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center">
                    <span className="text-xl">🎓</span>
                  </div>
                  <div>
                    <h4 className="font-semibold">Youth Exchange Program</h4>
                    <p className="text-sm text-neutral-600 dark:text-neutral-400">
                      50 young athletes, 10 countries
                    </p>
                  </div>
                </div>
                <p className="text-neutral-600 dark:text-neutral-400">
                  "Our cultural exchange program has created lasting friendships and 
                  professional networks among young athletes from across Africa and Europe, 
                  fostering international understanding."
                </p>
                <div className="mt-4 text-sm text-neutral-500">
                  - International Youth Sports Foundation
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Call to Action */}
        <Card className="bg-gradient-to-r from-primary-50 to-accent-50 dark:from-primary-900/20 dark:to-accent-900/20 border-primary-200 dark:border-primary-800">
          <CardContent className="p-8 text-center">
            <h3 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100 mb-4">
              Build Bridges Through Sports
            </h3>
            <p className="text-neutral-600 dark:text-neutral-400 mb-6 max-w-2xl mx-auto">
              Join a global community of sports diplomats, cultural ambassadors, and peace builders. 
              Create connections that transcend borders and foster international understanding.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" asChild>
                <Link href="/networking/networks">
                  Join Networks
                </Link>
              </Button>
              <Button size="lg" variant="outline" asChild>
                <Link href="/networking/events">
                  Attend Events
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export async function generateMetadata() {
  return {
    title: 'Networking & Diplomatic Bridges - Glbiashara',
    description: 'Build international connections, foster diplomatic relationships, and create cultural exchange opportunities through sports. Join global networks and events.'
  }
}
