import Link from 'next/link'
import Image from 'next/image'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { getInvestments } from '@/app/actions/investment'

export default async function InvestmentsPage() {
  const { investments } = await getInvestments()

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'low': return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-300'
      case 'medium': return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300'
      case 'high': return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-300'
      default: return 'text-neutral-600 bg-neutral-100 dark:bg-neutral-800 dark:text-neutral-300'
    }
  }

  return (
    <div className="min-h-screen bg-neutral-50 dark:bg-neutral-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-neutral-900 dark:text-neutral-100 mb-4">
            Trade & Investment Facilitation
          </h1>
          <p className="text-xl text-neutral-600 dark:text-neutral-400 max-w-3xl mx-auto">
            Connect investors with sports-related opportunities, facilitate business partnerships, 
            and drive economic growth through the power of football and sports ecosystem.
          </p>
        </div>

        {/* Key Features */}
        <div className="grid gap-6 md:grid-cols-4 mb-12">
          <Card className="text-center">
            <CardHeader>
              <div className="w-16 h-16 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">💰</span>
              </div>
              <CardTitle>Investment Opportunities</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-neutral-600 dark:text-neutral-400">
                Discover funding opportunities in sports infrastructure, academies, and technology.
              </p>
            </CardContent>
          </Card>

          <Card className="text-center">
            <CardHeader>
              <div className="w-16 h-16 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🤝</span>
              </div>
              <CardTitle>Business Partnerships</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-neutral-600 dark:text-neutral-400">
                Form strategic partnerships between clubs, academies, companies, and institutions.
              </p>
            </CardContent>
          </Card>

          <Card className="text-center">
            <CardHeader>
              <div className="w-16 h-16 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">📈</span>
              </div>
              <CardTitle>Trade Facilitation</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-neutral-600 dark:text-neutral-400">
                Enable trade in sports equipment, services, and technology across borders.
              </p>
            </CardContent>
          </Card>

          <Card className="text-center">
            <CardHeader>
              <div className="w-16 h-16 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🌍</span>
              </div>
              <CardTitle>Economic Growth</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-neutral-600 dark:text-neutral-400">
                Drive economic development through sports-based business opportunities.
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Investment Categories */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100 mb-6">
            Investment Categories
          </h2>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card className="hover:shadow-lg transition-shadow cursor-pointer">
              <CardContent className="p-6 text-center">
                <div className="text-3xl mb-2">🏟️</div>
                <h3 className="font-semibold mb-2">Sports Infrastructure</h3>
                <p className="text-sm text-neutral-600 dark:text-neutral-400">
                  Stadiums, training facilities, sports complexes
                </p>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-shadow cursor-pointer">
              <CardContent className="p-6 text-center">
                <div className="text-3xl mb-2">💻</div>
                <h3 className="font-semibold mb-2">Technology</h3>
                <p className="text-sm text-neutral-600 dark:text-neutral-400">
                  Sports tech, apps, analytics platforms
                </p>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-shadow cursor-pointer">
              <CardContent className="p-6 text-center">
                <div className="text-3xl mb-2">🎓</div>
                <h3 className="font-semibold mb-2">Education</h3>
                <p className="text-sm text-neutral-600 dark:text-neutral-400">
                  Sports academies, training programs
                </p>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-shadow cursor-pointer">
              <CardContent className="p-6 text-center">
                <div className="text-3xl mb-2">🌱</div>
                <h3 className="font-semibold mb-2">Agriculture</h3>
                <p className="text-sm text-neutral-600 dark:text-neutral-400">
                  Sports nutrition, agro-processing
                </p>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Active Investment Opportunities */}
        <div className="mb-12">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100">
              Active Investment Opportunities
            </h2>
            <Button asChild>
              <Link href="/investments/create">
                Create Investment
              </Link>
            </Button>
          </div>

          {investments && investments.length > 0 ? (
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {investments.map((investment) => (
                <Card key={investment.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <CardTitle className="text-lg mb-2">{investment.title}</CardTitle>
                        <div className="flex items-center space-x-2 text-sm mb-2">
                          <span className="bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300 px-2 py-1 rounded">
                            {investment.type}
                          </span>
                          <span className="bg-neutral-100 dark:bg-neutral-800 px-2 py-1 rounded">
                            {investment.category}
                          </span>
                          <span className={`px-2 py-1 rounded text-xs ${getRiskColor(investment.riskLevel)}`}>
                            {investment.riskLevel} risk
                          </span>
                        </div>
                      </div>
                      {investment.user.avatar && (
                        <Image
                          src={investment.user.avatar}
                          alt={`${investment.user.firstName} ${investment.user.lastName}`}
                          width={40}
                          height={40}
                          className="rounded-full"
                        />
                      )}
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-neutral-600 dark:text-neutral-400 mb-4 line-clamp-3">
                      {investment.description}
                    </p>
                    
                    <div className="space-y-2 mb-4">
                      <div className="flex justify-between text-sm">
                        <span className="text-neutral-500">Investment Amount:</span>
                        <span className="font-semibold">
                          {formatCurrency(investment.amount, investment.currency)}
                        </span>
                      </div>
                      {investment.targetAmount && (
                        <div className="flex justify-between text-sm">
                          <span className="text-neutral-500">Target:</span>
                          <span className="font-semibold">
                            {formatCurrency(investment.targetAmount, investment.currency)}
                          </span>
                        </div>
                      )}
                      <div className="flex justify-between text-sm">
                        <span className="text-neutral-500">Raised:</span>
                        <span className="font-semibold text-green-600">
                          {formatCurrency(investment.raisedAmount, investment.currency)}
                        </span>
                      </div>
                      {investment.expectedReturn && (
                        <div className="flex justify-between text-sm">
                          <span className="text-neutral-500">Expected Return:</span>
                          <span className="font-semibold text-primary-600">
                            {investment.expectedReturn}%
                          </span>
                        </div>
                      )}
                    </div>

                    {investment.targetAmount && (
                      <div className="mb-4">
                        <div className="flex justify-between text-xs text-neutral-500 mb-1">
                          <span>Progress</span>
                          <span>
                            {Math.round((investment.raisedAmount / investment.targetAmount) * 100)}%
                          </span>
                        </div>
                        <div className="w-full bg-neutral-200 dark:bg-neutral-700 rounded-full h-2">
                          <div
                            className="bg-primary-600 h-2 rounded-full"
                            style={{
                              width: `${Math.min((investment.raisedAmount / investment.targetAmount) * 100, 100)}%`
                            }}
                          />
                        </div>
                      </div>
                    )}

                    <div className="flex items-center justify-between text-sm text-neutral-500 mb-4">
                      <span>{investment._count.applications} applications</span>
                      <span>{investment.timeline}</span>
                    </div>

                    <div className="flex space-x-2">
                      <Button size="sm" className="flex-1" asChild>
                        <Link href={`/investments/${investment.id}`}>
                          View Details
                        </Link>
                      </Button>
                      <Button size="sm" variant="outline" asChild>
                        <Link href={`/investments/${investment.id}/apply`}>
                          Apply
                        </Link>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="p-12 text-center">
                <div className="text-6xl mb-4">💰</div>
                <h3 className="text-xl font-semibold mb-2">No Investment Opportunities</h3>
                <p className="text-neutral-600 dark:text-neutral-400 mb-6">
                  Be the first to create an investment opportunity in the sports ecosystem.
                </p>
                <Button asChild>
                  <Link href="/investments/create">
                    Create Investment Opportunity
                  </Link>
                </Button>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Success Stories */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100 mb-6">
            Investment Success Stories
          </h2>
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-4 mb-4">
                  <div className="w-12 h-12 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center">
                    <span className="text-xl">🏟️</span>
                  </div>
                  <div>
                    <h4 className="font-semibold">Modern Training Facility</h4>
                    <p className="text-sm text-neutral-600 dark:text-neutral-400">
                      $500K investment, 200% ROI in 2 years
                    </p>
                  </div>
                </div>
                <p className="text-neutral-600 dark:text-neutral-400">
                  "Our investment in the Dar es Salaam Football Academy's new training facility 
                  has exceeded all expectations. The facility now hosts international tournaments 
                  and generates significant revenue."
                </p>
                <div className="mt-4 text-sm text-neutral-500">
                  - International Sports Investors Group
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-4 mb-4">
                  <div className="w-12 h-12 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center">
                    <span className="text-xl">💻</span>
                  </div>
                  <div>
                    <h4 className="font-semibold">Sports Tech Startup</h4>
                    <p className="text-sm text-neutral-600 dark:text-neutral-400">
                      $250K seed funding, now serving 50+ academies
                    </p>
                  </div>
                </div>
                <p className="text-neutral-600 dark:text-neutral-400">
                  "The AI-powered performance analytics platform we funded is now used by 
                  academies across East Africa, helping young athletes reach their potential."
                </p>
                <div className="mt-4 text-sm text-neutral-500">
                  - TechSport Ventures
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Call to Action */}
        <Card className="bg-gradient-to-r from-primary-50 to-accent-50 dark:from-primary-900/20 dark:to-accent-900/20 border-primary-200 dark:border-primary-800">
          <CardContent className="p-8 text-center">
            <h3 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100 mb-4">
              Ready to Invest in the Future of Sports?
            </h3>
            <p className="text-neutral-600 dark:text-neutral-400 mb-6 max-w-2xl mx-auto">
              Join investors and entrepreneurs who are building the sports economy of tomorrow. 
              Create opportunities, form partnerships, and drive growth through sports.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" asChild>
                <Link href="/investments/opportunities">
                  Browse Opportunities
                </Link>
              </Button>
              <Button size="lg" variant="outline" asChild>
                <Link href="/partnerships">
                  Find Partners
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export async function generateMetadata() {
  return {
    title: 'Trade & Investment Facilitation - Glbiashara',
    description: 'Connect with investment opportunities, form business partnerships, and drive economic growth through sports. Discover funding for academies, infrastructure, and technology.'
  }
}
