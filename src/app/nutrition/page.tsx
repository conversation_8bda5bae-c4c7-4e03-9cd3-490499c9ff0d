import Link from 'next/link'
import Image from 'next/image'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { getNutritionProducts } from '@/app/actions/nutrition'

export default async function NutritionPage() {
  const { products } = await getNutritionProducts()

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2
    }).format(amount)
  }

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <span key={i} className={i < Math.floor(rating) ? 'text-yellow-400' : 'text-neutral-300'}>
        ★
      </span>
    ))
  }

  return (
    <div className="min-h-screen bg-neutral-50 dark:bg-neutral-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-neutral-900 dark:text-neutral-100 mb-4">
            Agro-processing & Sports Nutrition
          </h1>
          <p className="text-xl text-neutral-600 dark:text-neutral-400 max-w-3xl mx-auto">
            Discover premium sports nutrition products, connect with local agro-processors, 
            and build a sustainable supply chain for athletic performance and health.
          </p>
        </div>

        {/* Key Features */}
        <div className="grid gap-6 md:grid-cols-4 mb-12">
          <Card className="text-center">
            <CardHeader>
              <div className="w-16 h-16 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🥤</span>
              </div>
              <CardTitle>Sports Nutrition</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-neutral-600 dark:text-neutral-400">
                Premium supplements, energy drinks, and nutrition products for athletes.
              </p>
            </CardContent>
          </Card>

          <Card className="text-center">
            <CardHeader>
              <div className="w-16 h-16 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🌾</span>
              </div>
              <CardTitle>Local Sourcing</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-neutral-600 dark:text-neutral-400">
                Connect with local farmers and agro-processors for fresh, quality ingredients.
              </p>
            </CardContent>
          </Card>

          <Card className="text-center">
            <CardHeader>
              <div className="w-16 h-16 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🔗</span>
              </div>
              <CardTitle>Supply Chain</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-neutral-600 dark:text-neutral-400">
                Transparent supply chain tracking from farm to athlete.
              </p>
            </CardContent>
          </Card>

          <Card className="text-center">
            <CardHeader>
              <div className="w-16 h-16 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">✅</span>
              </div>
              <CardTitle>Quality Assured</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-neutral-600 dark:text-neutral-400">
                Certified organic, halal, and quality-tested products for peak performance.
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Product Categories */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100 mb-6">
            Product Categories
          </h2>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card className="hover:shadow-lg transition-shadow cursor-pointer">
              <CardContent className="p-6 text-center">
                <div className="text-3xl mb-2">💊</div>
                <h3 className="font-semibold mb-2">Supplements</h3>
                <p className="text-sm text-neutral-600 dark:text-neutral-400">
                  Protein powders, vitamins, minerals
                </p>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-shadow cursor-pointer">
              <CardContent className="p-6 text-center">
                <div className="text-3xl mb-2">🥤</div>
                <h3 className="font-semibold mb-2">Energy Drinks</h3>
                <p className="text-sm text-neutral-600 dark:text-neutral-400">
                  Pre-workout, post-workout, hydration
                </p>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-shadow cursor-pointer">
              <CardContent className="p-6 text-center">
                <div className="text-3xl mb-2">🍫</div>
                <h3 className="font-semibold mb-2">Protein Bars</h3>
                <p className="text-sm text-neutral-600 dark:text-neutral-400">
                  Energy bars, protein snacks, recovery foods
                </p>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-shadow cursor-pointer">
              <CardContent className="p-6 text-center">
                <div className="text-3xl mb-2">🥗</div>
                <h3 className="font-semibold mb-2">Organic Foods</h3>
                <p className="text-sm text-neutral-600 dark:text-neutral-400">
                  Natural, organic, locally-sourced foods
                </p>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Featured Products */}
        <div className="mb-12">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100">
              Featured Products
            </h2>
            <Button asChild>
              <Link href="/nutrition/create">
                Add Product
              </Link>
            </Button>
          </div>

          {products && products.length > 0 ? (
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {products.map((product) => (
                <Card key={product.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="relative">
                      {product.mediaUrls.length > 0 && (
                        <div className="aspect-video relative mb-4 rounded-lg overflow-hidden">
                          <Image
                            src={product.mediaUrls[0]}
                            alt={product.title}
                            fill
                            className="object-cover"
                          />
                        </div>
                      )}
                      <CardTitle className="text-lg mb-2">{product.title}</CardTitle>
                      <div className="flex items-center space-x-2 text-sm mb-2">
                        <span className="bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300 px-2 py-1 rounded">
                          {product.category}
                        </span>
                        <span className="bg-neutral-100 dark:bg-neutral-800 px-2 py-1 rounded">
                          {product.type}
                        </span>
                      </div>
                      {product.certifications.length > 0 && (
                        <div className="flex flex-wrap gap-1 mb-2">
                          {product.certifications.map((cert, index) => (
                            <span
                              key={index}
                              className="text-xs bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300 px-2 py-1 rounded"
                            >
                              {cert}
                            </span>
                          ))}
                        </div>
                      )}
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-neutral-600 dark:text-neutral-400 mb-4 line-clamp-3">
                      {product.description}
                    </p>
                    
                    <div className="flex items-center justify-between mb-4">
                      <div className="text-2xl font-bold text-primary-600">
                        {formatCurrency(product.price, product.currency)}
                        <span className="text-sm text-neutral-500 font-normal">
                          /{product.unit}
                        </span>
                      </div>
                      {product.averageRating > 0 && (
                        <div className="flex items-center space-x-1">
                          <div className="flex">
                            {renderStars(product.averageRating)}
                          </div>
                          <span className="text-sm text-neutral-500">
                            ({product._count.reviews})
                          </span>
                        </div>
                      )}
                    </div>

                    <div className="flex items-center space-x-2 mb-4">
                      {product.user.avatar && (
                        <Image
                          src={product.user.avatar}
                          alt={`${product.user.firstName} ${product.user.lastName}`}
                          width={24}
                          height={24}
                          className="rounded-full"
                        />
                      )}
                      <span className="text-sm text-neutral-600 dark:text-neutral-400">
                        by {product.user.firstName} {product.user.lastName}
                      </span>
                    </div>

                    <div className="flex items-center justify-between text-sm text-neutral-500 mb-4">
                      <span>{product._count.orders} orders</span>
                      <span>Target: {product.targetAudience.join(', ')}</span>
                    </div>

                    <div className="flex space-x-2">
                      <Button size="sm" className="flex-1" asChild>
                        <Link href={`/nutrition/products/${product.id}`}>
                          View Details
                        </Link>
                      </Button>
                      <Button size="sm" variant="outline" asChild>
                        <Link href={`/nutrition/products/${product.id}/order`}>
                          Order Now
                        </Link>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="p-12 text-center">
                <div className="text-6xl mb-4">🥤</div>
                <h3 className="text-xl font-semibold mb-2">No Products Available</h3>
                <p className="text-neutral-600 dark:text-neutral-400 mb-6">
                  Be the first to add a sports nutrition product to the marketplace.
                </p>
                <Button asChild>
                  <Link href="/nutrition/create">
                    Add First Product
                  </Link>
                </Button>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Supply Chain Transparency */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100 mb-6">
            Supply Chain Transparency
          </h2>
          <div className="grid gap-6 md:grid-cols-3">
            <Card>
              <CardContent className="p-6 text-center">
                <div className="w-16 h-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">🌱</span>
                </div>
                <h3 className="font-semibold mb-2">Farm to Table</h3>
                <p className="text-neutral-600 dark:text-neutral-400">
                  Track your nutrition products from local farms to your training table.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6 text-center">
                <div className="w-16 h-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">🏭</span>
                </div>
                <h3 className="font-semibold mb-2">Processing</h3>
                <p className="text-neutral-600 dark:text-neutral-400">
                  Verified processing facilities with quality certifications and standards.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6 text-center">
                <div className="w-16 h-16 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">🚚</span>
                </div>
                <h3 className="font-semibold mb-2">Distribution</h3>
                <p className="text-neutral-600 dark:text-neutral-400">
                  Efficient distribution network ensuring freshness and quality delivery.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Success Stories */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100 mb-6">
            Success Stories
          </h2>
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-4 mb-4">
                  <div className="w-12 h-12 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center">
                    <span className="text-xl">🏃‍♂️</span>
                  </div>
                  <div>
                    <h4 className="font-semibold">Enhanced Performance</h4>
                    <p className="text-sm text-neutral-600 dark:text-neutral-400">
                      25% improvement in endurance
                    </p>
                  </div>
                </div>
                <p className="text-neutral-600 dark:text-neutral-400">
                  "Since switching to locally-sourced organic sports nutrition, our academy's 
                  athletes have shown remarkable improvement in performance and recovery times."
                </p>
                <div className="mt-4 text-sm text-neutral-500">
                  - Mwanza Football Academy Coach
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-4 mb-4">
                  <div className="w-12 h-12 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center">
                    <span className="text-xl">🌾</span>
                  </div>
                  <div>
                    <h4 className="font-semibold">Local Economic Growth</h4>
                    <p className="text-sm text-neutral-600 dark:text-neutral-400">
                      Supporting 50+ local farmers
                    </p>
                  </div>
                </div>
                <p className="text-neutral-600 dark:text-neutral-400">
                  "Our agro-processing partnership has created sustainable income for local 
                  farmers while providing high-quality nutrition products for athletes."
                </p>
                <div className="mt-4 text-sm text-neutral-500">
                  - TanzaNutrition Co-operative
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Call to Action */}
        <Card className="bg-gradient-to-r from-primary-50 to-accent-50 dark:from-primary-900/20 dark:to-accent-900/20 border-primary-200 dark:border-primary-800">
          <CardContent className="p-8 text-center">
            <h3 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100 mb-4">
              Fuel Your Performance with Quality Nutrition
            </h3>
            <p className="text-neutral-600 dark:text-neutral-400 mb-6 max-w-2xl mx-auto">
              Join athletes, farmers, and processors who are building a sustainable sports 
              nutrition ecosystem. Discover products that enhance performance while supporting local communities.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" asChild>
                <Link href="/nutrition/products">
                  Shop Products
                </Link>
              </Button>
              <Button size="lg" variant="outline" asChild>
                <Link href="/nutrition/suppliers">
                  Find Suppliers
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export async function generateMetadata() {
  return {
    title: 'Agro-processing & Sports Nutrition - Glbiashara',
    description: 'Discover premium sports nutrition products, connect with local agro-processors, and build sustainable supply chains for athletic performance and health.'
  }
}
