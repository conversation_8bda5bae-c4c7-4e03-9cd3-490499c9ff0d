import Link from 'next/link'
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>eader, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { getEducationalPrograms } from '@/app/actions/education'

export default async function EducationPage() {
  const { programs } = await getEducationalPrograms()

  return (
    <div className="min-h-screen bg-neutral-50 dark:bg-neutral-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-neutral-900 dark:text-neutral-100 mb-4">
            Football for Education & Skills Development
          </h1>
          <p className="text-xl text-neutral-600 dark:text-neutral-400 max-w-3xl mx-auto">
            Combining the power of football with education to develop life skills, academic knowledge, 
            and professional capabilities for the next generation.
          </p>
        </div>

        {/* Key Features */}
        <div className="grid gap-6 md:grid-cols-3 mb-12">
          <Card className="text-center">
            <CardHeader>
              <div className="w-16 h-16 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🎓</span>
              </div>
              <CardTitle>Academic Integration</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-neutral-600 dark:text-neutral-400">
                Combine football training with mathematics, science, and language learning for holistic development.
              </p>
            </CardContent>
          </Card>

          <Card className="text-center">
            <CardHeader>
              <div className="w-16 h-16 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🏆</span>
              </div>
              <CardTitle>Life Skills</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-neutral-600 dark:text-neutral-400">
                Develop leadership, teamwork, communication, and problem-solving skills through sports-based learning.
              </p>
            </CardContent>
          </Card>

          <Card className="text-center">
            <CardHeader>
              <div className="w-16 h-16 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">📜</span>
              </div>
              <CardTitle>Digital Certificates</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-neutral-600 dark:text-neutral-400">
                Earn verified digital certificates and credentials recognized by academies, institutions, and employers.
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Program Categories */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100 mb-6">
            Program Categories
          </h2>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card className="hover:shadow-lg transition-shadow cursor-pointer">
              <CardContent className="p-6 text-center">
                <div className="text-3xl mb-2">📚</div>
                <h3 className="font-semibold mb-2">Academic</h3>
                <p className="text-sm text-neutral-600 dark:text-neutral-400">
                  Math, Science, Languages integrated with football
                </p>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-shadow cursor-pointer">
              <CardContent className="p-6 text-center">
                <div className="text-3xl mb-2">🤝</div>
                <h3 className="font-semibold mb-2">Life Skills</h3>
                <p className="text-sm text-neutral-600 dark:text-neutral-400">
                  Leadership, teamwork, communication
                </p>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-shadow cursor-pointer">
              <CardContent className="p-6 text-center">
                <div className="text-3xl mb-2">⚽</div>
                <h3 className="font-semibold mb-2">Technical</h3>
                <p className="text-sm text-neutral-600 dark:text-neutral-400">
                  Football skills, tactics, coaching
                </p>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-shadow cursor-pointer">
              <CardContent className="p-6 text-center">
                <div className="text-3xl mb-2">👑</div>
                <h3 className="font-semibold mb-2">Leadership</h3>
                <p className="text-sm text-neutral-600 dark:text-neutral-400">
                  Captain skills, mentoring, decision-making
                </p>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Available Programs */}
        <div className="mb-12">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100">
              Available Programs
            </h2>
            <Button asChild>
              <Link href="/education/create">
                Create Program
              </Link>
            </Button>
          </div>

          {programs && programs.length > 0 ? (
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {programs.map((program) => (
                <Card key={program.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div>
                        <CardTitle className="text-lg mb-2">{program.title}</CardTitle>
                        <div className="flex items-center space-x-2 text-sm text-neutral-600 dark:text-neutral-400">
                          <span className="bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300 px-2 py-1 rounded">
                            {program.category}
                          </span>
                          <span className="bg-neutral-100 dark:bg-neutral-800 px-2 py-1 rounded">
                            {program.level}
                          </span>
                        </div>
                      </div>
                      {(program.academy || program.institution) && (
                        <div className="text-right">
                          {program.academy && (
                            <div className="text-xs text-neutral-500">
                              {program.academy.name}
                            </div>
                          )}
                          {program.institution && (
                            <div className="text-xs text-neutral-500">
                              {program.institution.name}
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-neutral-600 dark:text-neutral-400 mb-4 line-clamp-3">
                      {program.description}
                    </p>
                    <div className="flex items-center justify-between text-sm text-neutral-500 mb-4">
                      <span>{program.duration} hours</span>
                      <span>{program._count.enrollments} enrolled</span>
                    </div>
                    <div className="flex space-x-2">
                      <Button size="sm" className="flex-1" asChild>
                        <Link href={`/education/programs/${program.id}`}>
                          View Details
                        </Link>
                      </Button>
                      <Button size="sm" variant="outline" asChild>
                        <Link href={`/education/programs/${program.id}/enroll`}>
                          Enroll
                        </Link>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="p-12 text-center">
                <div className="text-6xl mb-4">📚</div>
                <h3 className="text-xl font-semibold mb-2">No Programs Available</h3>
                <p className="text-neutral-600 dark:text-neutral-400 mb-6">
                  Be the first to create an educational program that combines football with learning.
                </p>
                <Button asChild>
                  <Link href="/education/create">
                    Create First Program
                  </Link>
                </Button>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Success Stories */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100 mb-6">
            Success Stories
          </h2>
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-4 mb-4">
                  <div className="w-12 h-12 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center">
                    <span className="text-xl">👨‍🎓</span>
                  </div>
                  <div>
                    <h4 className="font-semibold">Mathematics Through Football</h4>
                    <p className="text-sm text-neutral-600 dark:text-neutral-400">
                      Improved math scores by 40% using football statistics
                    </p>
                  </div>
                </div>
                <p className="text-neutral-600 dark:text-neutral-400">
                  "Learning angles and geometry through football tactics made math so much more interesting. 
                  I never thought I could love both football and mathematics!"
                </p>
                <div className="mt-4 text-sm text-neutral-500">
                  - Student from Dar es Salaam Academy
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-4 mb-4">
                  <div className="w-12 h-12 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center">
                    <span className="text-xl">👩‍💼</span>
                  </div>
                  <div>
                    <h4 className="font-semibold">Leadership Development</h4>
                    <p className="text-sm text-neutral-600 dark:text-neutral-400">
                      From team captain to business leader
                    </p>
                  </div>
                </div>
                <p className="text-neutral-600 dark:text-neutral-400">
                  "The leadership skills I learned as team captain helped me start my own business. 
                  Football taught me how to motivate people and make tough decisions."
                </p>
                <div className="mt-4 text-sm text-neutral-500">
                  - Graduate, now CEO of tech startup
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Call to Action */}
        <Card className="bg-gradient-to-r from-primary-50 to-accent-50 dark:from-primary-900/20 dark:to-accent-900/20 border-primary-200 dark:border-primary-800">
          <CardContent className="p-8 text-center">
            <h3 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100 mb-4">
              Ready to Transform Education Through Football?
            </h3>
            <p className="text-neutral-600 dark:text-neutral-400 mb-6 max-w-2xl mx-auto">
              Join thousands of students, coaches, and educators who are revolutionizing learning 
              by combining the beautiful game with academic excellence.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" asChild>
                <Link href="/education/programs">
                  Browse Programs
                </Link>
              </Button>
              <Button size="lg" variant="outline" asChild>
                <Link href="/academies">
                  Find Academy
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export async function generateMetadata() {
  return {
    title: 'Football for Education & Skills Development - Glbiashara',
    description: 'Combine football training with academic learning and life skills development. Earn digital certificates and build a brighter future through sports-based education.'
  }
}
