import Link from 'next/link'
import Image from 'next/image'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { getTechInnovations, getInfrastructureProjects } from '@/app/actions/technology'

export default async function TechnologyPage() {
  const [
    { innovations },
    { projects }
  ] = await Promise.all([
    getTechInnovations(),
    getInfrastructureProjects()
  ])

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  const getStageColor = (stage: string) => {
    const colors: { [key: string]: string } = {
      'concept': 'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300',
      'prototype': 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300',
      'development': 'bg-yellow-100 dark:bg-yellow-900 text-yellow-700 dark:text-yellow-300',
      'testing': 'bg-orange-100 dark:bg-orange-900 text-orange-700 dark:text-orange-300',
      'production': 'bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300'
    }
    return colors[stage] || 'bg-neutral-100 dark:bg-neutral-800'
  }

  const getStatusColor = (status: string) => {
    const colors: { [key: string]: string } = {
      'planning': 'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300',
      'design': 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300',
      'construction': 'bg-yellow-100 dark:bg-yellow-900 text-yellow-700 dark:text-yellow-300',
      'testing': 'bg-orange-100 dark:bg-orange-900 text-orange-700 dark:text-orange-300',
      'operational': 'bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300'
    }
    return colors[status] || 'bg-neutral-100 dark:bg-neutral-800'
  }

  return (
    <div className="min-h-screen bg-neutral-50 dark:bg-neutral-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-neutral-900 dark:text-neutral-100 mb-4">
            Technology & Infrastructure Growth
          </h1>
          <p className="text-xl text-neutral-600 dark:text-neutral-400 max-w-3xl mx-auto">
            Drive innovation in sports technology, build smart infrastructure, and accelerate 
            digital transformation across academies, clubs, and sporting communities.
          </p>
        </div>

        {/* Key Features */}
        <div className="grid gap-6 md:grid-cols-4 mb-12">
          <Card className="text-center">
            <CardHeader>
              <div className="w-16 h-16 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">💡</span>
              </div>
              <CardTitle>Innovation Hub</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-neutral-600 dark:text-neutral-400">
                Showcase cutting-edge sports technology and digital solutions.
              </p>
            </CardContent>
          </Card>

          <Card className="text-center">
            <CardHeader>
              <div className="w-16 h-16 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🏗️</span>
              </div>
              <CardTitle>Smart Infrastructure</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-neutral-600 dark:text-neutral-400">
                Build modern, sustainable sports facilities with smart technology.
              </p>
            </CardContent>
          </Card>

          <Card className="text-center">
            <CardHeader>
              <div className="w-16 h-16 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🔗</span>
              </div>
              <CardTitle>Digital Transformation</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-neutral-600 dark:text-neutral-400">
                Accelerate digitization of sports organizations and processes.
              </p>
            </CardContent>
          </Card>

          <Card className="text-center">
            <CardHeader>
              <div className="w-16 h-16 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">📊</span>
              </div>
              <CardTitle>Data Analytics</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-neutral-600 dark:text-neutral-400">
                Leverage data science for performance optimization and insights.
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Innovation Categories */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100 mb-6">
            Innovation Categories
          </h2>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card className="hover:shadow-lg transition-shadow cursor-pointer">
              <CardContent className="p-6 text-center">
                <div className="text-3xl mb-2">📱</div>
                <h3 className="font-semibold mb-2">Mobile Apps</h3>
                <p className="text-sm text-neutral-600 dark:text-neutral-400">
                  Training apps, performance tracking, fan engagement
                </p>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-shadow cursor-pointer">
              <CardContent className="p-6 text-center">
                <div className="text-3xl mb-2">🤖</div>
                <h3 className="font-semibold mb-2">AI & ML</h3>
                <p className="text-sm text-neutral-600 dark:text-neutral-400">
                  Performance analysis, injury prevention, talent scouting
                </p>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-shadow cursor-pointer">
              <CardContent className="p-6 text-center">
                <div className="text-3xl mb-2">🥽</div>
                <h3 className="font-semibold mb-2">VR/AR</h3>
                <p className="text-sm text-neutral-600 dark:text-neutral-400">
                  Virtual training, immersive experiences, tactical analysis
                </p>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-shadow cursor-pointer">
              <CardContent className="p-6 text-center">
                <div className="text-3xl mb-2">📡</div>
                <h3 className="font-semibold mb-2">IoT & Sensors</h3>
                <p className="text-sm text-neutral-600 dark:text-neutral-400">
                  Wearable devices, smart equipment, facility monitoring
                </p>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Tech Innovations */}
        <div className="mb-12">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100">
              Latest Tech Innovations
            </h2>
            <Button asChild>
              <Link href="/technology/create">
                Submit Innovation
              </Link>
            </Button>
          </div>

          {innovations && innovations.length > 0 ? (
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {innovations.map((innovation) => (
                <Card key={innovation.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="relative">
                      {innovation.mediaUrls.length > 0 && (
                        <div className="aspect-video relative mb-4 rounded-lg overflow-hidden">
                          <Image
                            src={innovation.mediaUrls[0]}
                            alt={innovation.title}
                            fill
                            className="object-cover"
                          />
                        </div>
                      )}
                      <CardTitle className="text-lg mb-2">{innovation.title}</CardTitle>
                      <div className="flex items-center space-x-2 text-sm mb-2">
                        <span className="bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300 px-2 py-1 rounded">
                          {innovation.category}
                        </span>
                        <span className={`px-2 py-1 rounded text-xs ${getStageColor(innovation.stage)}`}>
                          {innovation.stage}
                        </span>
                      </div>
                      <div className="flex flex-wrap gap-1 mb-2">
                        {innovation.techStack.slice(0, 3).map((tech, index) => (
                          <span
                            key={index}
                            className="text-xs bg-neutral-100 dark:bg-neutral-800 px-2 py-1 rounded"
                          >
                            {tech}
                          </span>
                        ))}
                        {innovation.techStack.length > 3 && (
                          <span className="text-xs text-neutral-500">
                            +{innovation.techStack.length - 3} more
                          </span>
                        )}
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-neutral-600 dark:text-neutral-400 mb-4 line-clamp-3">
                      {innovation.description}
                    </p>
                    
                    <div className="space-y-2 mb-4">
                      <div className="text-sm">
                        <span className="text-neutral-500">Target Users:</span>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {innovation.targetUsers.slice(0, 3).map((user, index) => (
                            <span
                              key={index}
                              className="text-xs bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 px-2 py-1 rounded"
                            >
                              {user}
                            </span>
                          ))}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2 mb-4">
                      {innovation.user.avatar && (
                        <Image
                          src={innovation.user.avatar}
                          alt={`${innovation.user.firstName} ${innovation.user.lastName}`}
                          width={24}
                          height={24}
                          className="rounded-full"
                        />
                      )}
                      <span className="text-sm text-neutral-600 dark:text-neutral-400">
                        by {innovation.user.firstName} {innovation.user.lastName}
                      </span>
                    </div>

                    <div className="flex items-center justify-between text-sm text-neutral-500 mb-4">
                      <span>{innovation._count.adoptions} adoptions</span>
                      <div className="flex space-x-2">
                        {innovation.isOpenSource && (
                          <span className="bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300 px-2 py-1 rounded text-xs">
                            Open Source
                          </span>
                        )}
                        {innovation.isCommercial && (
                          <span className="bg-purple-100 dark:bg-purple-900 text-purple-700 dark:text-purple-300 px-2 py-1 rounded text-xs">
                            Commercial
                          </span>
                        )}
                      </div>
                    </div>

                    <div className="flex space-x-2">
                      <Button size="sm" className="flex-1" asChild>
                        <Link href={`/technology/innovations/${innovation.id}`}>
                          View Details
                        </Link>
                      </Button>
                      <Button size="sm" variant="outline" asChild>
                        <Link href={`/technology/innovations/${innovation.id}/adopt`}>
                          Adopt
                        </Link>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="p-12 text-center">
                <div className="text-6xl mb-4">💡</div>
                <h3 className="text-xl font-semibold mb-2">No Innovations Available</h3>
                <p className="text-neutral-600 dark:text-neutral-400 mb-6">
                  Be the first to showcase a sports technology innovation.
                </p>
                <Button asChild>
                  <Link href="/technology/create">
                    Submit First Innovation
                  </Link>
                </Button>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Infrastructure Projects */}
        <div className="mb-12">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100">
              Infrastructure Development
            </h2>
            <Button variant="outline" asChild>
              <Link href="/technology/infrastructure">
                View All Projects
              </Link>
            </Button>
          </div>

          {projects && projects.length > 0 ? (
            <div className="grid gap-4 md:grid-cols-2">
              {projects.slice(0, 4).map((project) => (
                <Card key={project.id} className="hover:shadow-lg transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <h3 className="font-semibold text-lg mb-2">{project.title}</h3>
                        <div className="flex items-center space-x-2 text-sm mb-2">
                          <span className="bg-accent-100 dark:bg-accent-900 text-accent-700 dark:text-accent-300 px-2 py-1 rounded">
                            {project.type}
                          </span>
                          <span className={`px-2 py-1 rounded text-xs ${getStatusColor(project.status)}`}>
                            {project.status}
                          </span>
                        </div>
                        <div className="text-sm text-neutral-500 mb-2">
                          📍 {project.location}
                        </div>
                      </div>
                    </div>
                    <p className="text-neutral-600 dark:text-neutral-400 mb-4 line-clamp-2">
                      {project.description}
                    </p>
                    <div className="flex items-center justify-between">
                      <span className="text-lg font-semibold text-primary-600">
                        {formatCurrency(project.budget, project.currency)}
                      </span>
                      <Button size="sm" asChild>
                        <Link href={`/technology/infrastructure/${project.id}`}>
                          Learn More
                        </Link>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="p-8 text-center">
                <div className="text-4xl mb-2">🏗️</div>
                <p className="text-neutral-600 dark:text-neutral-400">
                  No infrastructure projects available. Check back soon for new developments.
                </p>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Success Stories */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100 mb-6">
            Technology Success Stories
          </h2>
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-4 mb-4">
                  <div className="w-12 h-12 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center">
                    <span className="text-xl">📱</span>
                  </div>
                  <div>
                    <h4 className="font-semibold">Smart Training Platform</h4>
                    <p className="text-sm text-neutral-600 dark:text-neutral-400">
                      50+ academies, 10,000+ athletes
                    </p>
                  </div>
                </div>
                <p className="text-neutral-600 dark:text-neutral-400">
                  "Our AI-powered training platform has revolutionized how academies track 
                  athlete progress, resulting in 40% improvement in training efficiency 
                  and better talent identification."
                </p>
                <div className="mt-4 text-sm text-neutral-500">
                  - TechSport Solutions
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-4 mb-4">
                  <div className="w-12 h-12 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center">
                    <span className="text-xl">🏟️</span>
                  </div>
                  <div>
                    <h4 className="font-semibold">Smart Stadium Initiative</h4>
                    <p className="text-sm text-neutral-600 dark:text-neutral-400">
                      5 stadiums, 100,000+ fans
                    </p>
                  </div>
                </div>
                <p className="text-neutral-600 dark:text-neutral-400">
                  "The smart stadium infrastructure project has enhanced fan experience 
                  with IoT sensors, mobile apps, and real-time analytics, increasing 
                  attendance by 30% and revenue by 25%."
                </p>
                <div className="mt-4 text-sm text-neutral-500">
                  - National Stadium Authority
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Call to Action */}
        <Card className="bg-gradient-to-r from-primary-50 to-accent-50 dark:from-primary-900/20 dark:to-accent-900/20 border-primary-200 dark:border-primary-800">
          <CardContent className="p-8 text-center">
            <h3 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100 mb-4">
              Shape the Future of Sports Technology
            </h3>
            <p className="text-neutral-600 dark:text-neutral-400 mb-6 max-w-2xl mx-auto">
              Join innovators, developers, and infrastructure builders who are transforming 
              sports through technology. Showcase your innovations and build the smart 
              sports facilities of tomorrow.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" asChild>
                <Link href="/technology/innovations">
                  Explore Innovations
                </Link>
              </Button>
              <Button size="lg" variant="outline" asChild>
                <Link href="/technology/infrastructure">
                  View Projects
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export async function generateMetadata() {
  return {
    title: 'Technology & Infrastructure Growth - Glbiashara',
    description: 'Drive innovation in sports technology, build smart infrastructure, and accelerate digital transformation across sporting communities.'
  }
}
