import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getCurrentUser } from '@/lib/auth'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const currentUser = await getCurrentUser()
    if (!currentUser) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params
    const academyId = parseInt(id)

    if (isNaN(academyId)) {
      return NextResponse.json({ error: 'Invalid academy ID' }, { status: 400 })
    }

    const { searchParams } = new URL(request.url)
    const timeRange = searchParams.get('timeRange') || 'month'

    // Calculate date range based on timeRange
    const now = new Date()
    let startDate: Date

    switch (timeRange) {
      case 'week':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        break
      case 'quarter':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
        break
      case 'year':
        startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000)
        break
      default: // month
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
    }

    // Verify academy exists and user has access
    const academy = await prisma.academy.findUnique({
      where: { id: academyId },
      select: { id: true, name: true }
    })

    if (!academy) {
      return NextResponse.json({ error: 'Academy not found' }, { status: 404 })
    }

    // Fetch member statistics
    const totalMembers = await prisma.academyMember.count({
      where: { academyId }
    })

    const activeMembers = await prisma.academyMember.count({
      where: {
        academyId,
        status: 'active'
      }
    })

    const newMembersThisMonth = await prisma.academyMember.count({
      where: {
        academyId,
        createdAt: {
          gte: startDate
        }
      }
    })

    // Member distribution by skill level
    const membersBySkillLevel = await prisma.academyMember.groupBy({
      by: ['skillLevel'],
      where: { academyId },
      _count: true
    })

    // Member distribution by position
    const membersByPosition = await prisma.academyMember.groupBy({
      by: ['position'],
      where: { 
        academyId,
        position: { not: null }
      },
      _count: true
    })

    // Performance statistics
    const totalPerformanceRecords = await prisma.performance.count({
      where: {
        academyMember: { academyId },
        recordedAt: { gte: startDate }
      }
    })

    // Calculate average improvement (simplified calculation)
    const performanceRecords = await prisma.performance.findMany({
      where: {
        academyMember: { academyId },
        recordedAt: { gte: startDate }
      },
      select: {
        value: true,
        metricType: true,
        academyMemberId: true
      }
    })

    // Group by member and metric to calculate improvements
    const memberMetrics: Record<string, Record<string, number[]>> = {}
    performanceRecords.forEach(record => {
      const key = `${record.academyMemberId}-${record.metricType}`
      if (!memberMetrics[key]) {
        memberMetrics[key] = { values: [] }
      }
      memberMetrics[key].values.push(record.value)
    })

    let totalImprovement = 0
    let improvementCount = 0
    Object.values(memberMetrics).forEach(metric => {
      if (metric.values.length >= 2) {
        const improvement = ((metric.values[metric.values.length - 1] - metric.values[0]) / metric.values[0]) * 100
        totalImprovement += improvement
        improvementCount++
      }
    })

    const averageImprovement = improvementCount > 0 ? totalImprovement / improvementCount : 0

    // Training statistics
    const totalTrainingSessions = await prisma.training.count({
      where: {
        academyId,
        scheduledAt: { gte: startDate }
      }
    })

    const completedSessions = await prisma.training.count({
      where: {
        academyId,
        status: 'completed',
        scheduledAt: { gte: startDate }
      }
    })

    // Training by type
    const trainingByType = await prisma.training.groupBy({
      by: ['type'],
      where: {
        academyId,
        scheduledAt: { gte: startDate }
      },
      _count: true
    })

    // Calculate average attendance
    const attendanceRecords = await prisma.trainingAttendance.findMany({
      where: {
        training: {
          academyId,
          scheduledAt: { gte: startDate }
        }
      },
      include: {
        training: true
      }
    })

    const attendanceByTraining: Record<number, { present: number; total: number }> = {}
    attendanceRecords.forEach(attendance => {
      if (!attendanceByTraining[attendance.trainingId]) {
        attendanceByTraining[attendance.trainingId] = { present: 0, total: 0 }
      }
      attendanceByTraining[attendance.trainingId].total++
      if (attendance.status === 'present') {
        attendanceByTraining[attendance.trainingId].present++
      }
    })

    const attendanceRates = Object.values(attendanceByTraining).map(
      ({ present, total }) => total > 0 ? (present / total) * 100 : 0
    )
    const averageAttendance = attendanceRates.length > 0 
      ? attendanceRates.reduce((sum, rate) => sum + rate, 0) / attendanceRates.length 
      : 0

    // Event statistics
    const totalEvents = await prisma.event.count({
      where: {
        academyId,
        startDate: { gte: startDate }
      }
    })

    const upcomingEvents = await prisma.event.count({
      where: {
        academyId,
        startDate: { gte: now }
      }
    })

    const eventsByType = await prisma.event.groupBy({
      by: ['type'],
      where: {
        academyId,
        startDate: { gte: startDate }
      },
      _count: true
    })

    // Partnership statistics
    const totalPartnerships = await prisma.academyPartnership.count({
      where: { academyId }
    })

    const activePartnerships = await prisma.academyPartnership.count({
      where: {
        academyId,
        status: 'active'
      }
    })

    const partnershipsByType = await prisma.academyPartnership.groupBy({
      by: ['partnershipType'],
      where: { academyId },
      _count: true
    })

    // Format the response
    const analytics = {
      memberStats: {
        totalMembers,
        activeMembers,
        newMembersThisMonth,
        membersBySkillLevel: Object.fromEntries(
          membersBySkillLevel.map(item => [item.skillLevel, item._count])
        ),
        membersByPosition: Object.fromEntries(
          membersByPosition.map(item => [item.position || 'unspecified', item._count])
        )
      },
      performanceStats: {
        totalPerformanceRecords,
        averageImprovement: Math.round(averageImprovement * 10) / 10,
        topPerformers: [], // TODO: Implement top performers calculation
        skillDistribution: {} // TODO: Implement skill distribution
      },
      trainingStats: {
        totalTrainingSessions,
        completedSessions,
        averageAttendance: Math.round(averageAttendance * 10) / 10,
        trainingByType: Object.fromEntries(
          trainingByType.map(item => [item.type, item._count])
        ),
        attendanceTrend: [] // TODO: Implement attendance trend
      },
      eventStats: {
        totalEvents,
        upcomingEvents,
        participationRate: 0, // TODO: Calculate participation rate
        eventsByType: Object.fromEntries(
          eventsByType.map(item => [item.type, item._count])
        )
      },
      partnershipStats: {
        totalPartnerships,
        activePartnerships,
        partnershipsByType: Object.fromEntries(
          partnershipsByType.map(item => [item.partnershipType, item._count])
        ),
        revenueFromPartnerships: 0 // TODO: Calculate revenue from partnerships
      }
    }

    return NextResponse.json(analytics)

  } catch (error) {
    console.error('Error fetching academy analytics:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
