import { notFound } from 'next/navigation'
import { getMemberProfile } from '@/app/actions/academies'
import { MemberProfile } from '@/components/academies/MemberProfile'

interface MemberProfilePageProps {
  params: Promise<{
    id: string
  }>
}

export default async function MemberProfilePage({ params }: MemberProfilePageProps) {
  const { id } = await params
  const memberId = parseInt(id)
  
  if (isNaN(memberId)) {
    notFound()
  }

  const result = await getMemberProfile(memberId)

  if (!result.success || !result.data) {
    notFound()
  }

  const member = result.data

  // Transform the data to match the component interface
  const transformedMember = {
    ...member,
    talentVideos: Array.isArray(member.talentVideos) ? member.talentVideos as string[] : [],
    personalInfo: member.personalInfo as any,
    medicalInfo: member.medicalInfo as any,
    emergencyContact: member.emergencyContact as any
  }

  return (
    <div className="min-h-screen bg-neutral-50 dark:bg-neutral-900">
      <div className="container mx-auto px-4 py-8">
        <MemberProfile member={transformedMember} />
      </div>
    </div>
  )
}

export async function generateMetadata({ params }: MemberProfilePageProps) {
  const { id } = await params
  const memberId = parseInt(id)
  
  if (isNaN(memberId)) {
    return {
      title: 'Member Not Found'
    }
  }

  const result = await getMemberProfile(memberId)

  if (!result.success || !result.data) {
    return {
      title: 'Member Not Found'
    }
  }

  const member = result.data

  return {
    title: `${member.user.firstName} ${member.user.lastName} - ${member.academy.name}`,
    description: `View ${member.user.firstName} ${member.user.lastName}'s profile at ${member.academy.name}. Track performance, achievements, and progress.`
  }
}
