import { <PERSON>, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { getAcademies } from '@/app/actions/academies'
import { AcademyOnboarding } from '@/components/academies/AcademyOnboarding'
import Link from 'next/link'

export default async function AcademiesPage() {
  const result = await getAcademies()
  
  if (!result.success || !result.data) {
    return (
      <div className="min-h-screen bg-neutral-50 dark:bg-neutral-900">
        <div className="container-mobile py-8">
          <div className="text-center">
            <h1 className="text-3xl font-bold text-neutral-900 dark:text-neutral-100">
              Sports Academies
            </h1>
            <p className="mt-4 text-neutral-600 dark:text-neutral-400">
              Unable to load academies at this time.
            </p>
          </div>
        </div>
      </div>
    )
  }

  const academies = result.data

  return (
    <div className="min-h-screen bg-neutral-50 dark:bg-neutral-900">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-primary-600 to-accent-600 py-16">
        <div className="container-mobile text-center text-white">
          <h1 className="mb-4 text-4xl font-bold md:text-6xl">
            Sports Academies
          </h1>
          <p className="mb-8 text-lg text-primary-100 md:text-xl">
            Discover and join Tanzania's premier sports academies. Develop your talent, track your progress, and connect with professional clubs.
          </p>
          <div className="flex flex-col gap-4 sm:flex-row sm:justify-center">
            <Button size="lg" variant="secondary" asChild>
              <Link href="#academies-grid">
                Find Your Academy
              </Link>
            </Button>
            <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-primary-600" asChild>
              <Link href="/academies/register">
                Register Your Academy
              </Link>
            </Button>
          </div>
        </div>
      </div>

      {/* Stats Section */}
      <div className="bg-white dark:bg-neutral-800 py-12">
        <div className="container-mobile">
          <div className="grid grid-cols-2 gap-8 md:grid-cols-4">
            <div className="text-center">
              <div className="text-3xl font-bold text-primary-600 dark:text-primary-400">
                {academies.length}
              </div>
              <div className="text-sm text-neutral-600 dark:text-neutral-400">
                Active Academies
              </div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-primary-600 dark:text-primary-400">
                {academies.reduce((total, academy) => total + academy._count.members, 0)}
              </div>
              <div className="text-sm text-neutral-600 dark:text-neutral-400">
                Total Members
              </div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-primary-600 dark:text-primary-400">
                {academies.reduce((total, academy) => total + academy._count.trainings, 0)}
              </div>
              <div className="text-sm text-neutral-600 dark:text-neutral-400">
                Training Sessions
              </div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-primary-600 dark:text-primary-400">
                {academies.reduce((total, academy) => total + academy._count.events, 0)}
              </div>
              <div className="text-sm text-neutral-600 dark:text-neutral-400">
                Upcoming Events
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Onboarding Section */}
      <div className="container-mobile py-12">
        <AcademyOnboarding />
      </div>

      {/* Academies Grid */}
      <div id="academies-grid" className="container-mobile py-12">
        <div className="mb-8 text-center">
          <h2 className="text-3xl font-bold text-neutral-900 dark:text-neutral-100">
            Featured Academies
          </h2>
          <p className="mt-2 text-neutral-600 dark:text-neutral-400">
            Join these top-rated sports academies and take your skills to the next level
          </p>
        </div>

        {academies.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">🏆</div>
            <h3 className="text-xl font-semibold text-neutral-900 dark:text-neutral-100 mb-2">
              No Academies Yet
            </h3>
            <p className="text-neutral-600 dark:text-neutral-400 mb-6">
              Be the first to register your sports academy on our platform
            </p>
            <Button>Register Your Academy</Button>
          </div>
        ) : (
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {academies.map((academy) => {
              const content = academy.content as any

              return (
                <Card key={academy.id} variant="elevated" className="overflow-hidden hover:shadow-lg transition-shadow">
                  <CardHeader className="pb-4">
                    <div className="flex items-center space-x-4">
                      <div className="flex h-16 w-16 items-center justify-center rounded-lg bg-primary-50 dark:bg-primary-900/20">
                        {academy.logo ? (
                          <img
                            src={academy.logo}
                            alt={`${academy.name} logo`}
                            className="h-12 w-12 object-contain"
                          />
                        ) : (
                          <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary-500 text-white font-bold text-lg">
                            {academy.name[0]}
                          </div>
                        )}
                      </div>
                      <div className="flex-1">
                        <CardTitle className="text-lg">{academy.name}</CardTitle>
                        <div className="flex items-center space-x-2 mt-1">
                          <span className="inline-flex items-center rounded-full bg-primary-100 dark:bg-primary-900/30 px-2.5 py-0.5 text-xs font-medium text-primary-800 dark:text-primary-200">
                            {academy.sport}
                          </span>
                          <span className="inline-flex items-center rounded-full bg-accent-100 dark:bg-accent-900/30 px-2.5 py-0.5 text-xs font-medium text-accent-800 dark:text-accent-200">
                            {academy.level}
                          </span>
                        </div>
                      </div>
                    </div>
                  </CardHeader>
                  
                  <CardContent className="space-y-4">
                    <CardDescription className="line-clamp-3">
                      {academy.description}
                    </CardDescription>
                    
                    <div className="flex items-center space-x-4 text-sm text-neutral-600 dark:text-neutral-400">
                      <div className="flex items-center space-x-1">
                        <span>📍</span>
                        <span>{academy.location}</span>
                      </div>
                    </div>

                    <div className="grid grid-cols-3 gap-4 text-center">
                      <div>
                        <div className="text-lg font-semibold text-neutral-900 dark:text-neutral-100">
                          {academy._count.members}
                        </div>
                        <div className="text-xs text-neutral-600 dark:text-neutral-400">
                          Members
                        </div>
                      </div>
                      <div>
                        <div className="text-lg font-semibold text-neutral-900 dark:text-neutral-100">
                          {academy._count.trainings}
                        </div>
                        <div className="text-xs text-neutral-600 dark:text-neutral-400">
                          Trainings
                        </div>
                      </div>
                      <div>
                        <div className="text-lg font-semibold text-neutral-900 dark:text-neutral-100">
                          {academy._count.events}
                        </div>
                        <div className="text-xs text-neutral-600 dark:text-neutral-400">
                          Events
                        </div>
                      </div>
                    </div>

                    <div className="flex space-x-2">
                      <Link href={`/academies/${academy.slug}`} className="flex-1">
                        <Button className="w-full" size="sm">
                          View Academy
                        </Button>
                      </Link>
                      <Button variant="outline" size="sm">
                        Join Now
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        )}
      </div>

      {/* Call to Action */}
      <div className="bg-neutral-100 dark:bg-neutral-800 py-16">
        <div className="container-mobile text-center">
          <h2 className="text-3xl font-bold text-neutral-900 dark:text-neutral-100 mb-4">
            Ready to Start Your Journey?
          </h2>
          <p className="text-lg text-neutral-600 dark:text-neutral-400 mb-8">
            Join a sports academy today and unlock your potential with professional training, performance tracking, and direct connections to clubs and sponsors.
          </p>
          <div className="flex flex-col gap-4 sm:flex-row sm:justify-center">
            <Button size="lg">
              Browse All Academies
            </Button>
            <Button size="lg" variant="outline">
              Register Your Academy
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
