import { notFound } from 'next/navigation'
import Link from 'next/link'
import { getAcademyEvent } from '@/app/actions/academies'
import { AcademyMediaManager } from '@/components/academies/AcademyMediaManager'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'

interface AcademyEventPageProps {
  params: Promise<{
    id: string
  }>
}

export default async function AcademyEventPage({ params }: AcademyEventPageProps) {
  const { id } = await params
  const eventId = parseInt(id)
  
  if (isNaN(eventId)) {
    notFound()
  }

  const result = await getAcademyEvent(eventId)

  if (!result.success || !result.data) {
    notFound()
  }

  const event = result.data

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'competition':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
      case 'tournament':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300'
      case 'friendly':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      case 'showcase':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
      case 'ceremony':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
    }
  }

  return (
    <div className="min-h-screen bg-neutral-50 dark:bg-neutral-900">
      <div className="container mx-auto px-4 py-8">
        {/* Breadcrumb */}
        <nav className="flex items-center space-x-2 text-sm text-neutral-600 dark:text-neutral-400 mb-6">
          <Link href="/academies" className="hover:text-primary-600">
            Academies
          </Link>
          <span>/</span>
          <Link href={`/academies/${event.academy.slug}`} className="hover:text-primary-600">
            {event.academy.name}
          </Link>
          <span>/</span>
          <span className="text-neutral-900 dark:text-neutral-100">Event</span>
        </nav>

        {/* Header */}
        <div className="bg-gradient-to-r from-primary-600 to-primary-700 rounded-lg p-6 text-white mb-8">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center space-x-3 mb-2">
                <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                </svg>
                <h1 className="text-3xl font-bold">{event.title}</h1>
              </div>
              <p className="text-primary-100 mb-4">{event.description}</p>
              <div className="flex items-center space-x-4">
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getTypeColor(event.type)}`}>
                  {event.type.charAt(0).toUpperCase() + event.type.slice(1)}
                </span>
                {event.isPublic && (
                  <span className="px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                    Public Event
                  </span>
                )}
              </div>
            </div>
            {event.academy.logo && (
              <img
                src={event.academy.logo}
                alt={event.academy.name}
                className="w-16 h-16 rounded-lg bg-white/20 p-2"
              />
            )}
          </div>
        </div>

        <div className="grid gap-8 lg:grid-cols-3">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Event Details */}
            <Card>
              <CardHeader>
                <CardTitle>Event Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="flex items-center space-x-2">
                    <svg className="w-5 h-5 text-neutral-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 4h6m-6 4h6m-6 4h6" />
                    </svg>
                    <div>
                      <p className="text-sm text-neutral-600 dark:text-neutral-400">Start Date</p>
                      <p className="font-medium">
                        {new Date(event.startDate).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <svg className="w-5 h-5 text-neutral-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 4h6m-6 4h6m-6 4h6" />
                    </svg>
                    <div>
                      <p className="text-sm text-neutral-600 dark:text-neutral-400">End Date</p>
                      <p className="font-medium">
                        {event.endDate ? new Date(event.endDate).toLocaleDateString() : 'Same day'}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <svg className="w-5 h-5 text-neutral-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <div>
                      <p className="text-sm text-neutral-600 dark:text-neutral-400">Time</p>
                      <p className="font-medium">
                        {new Date(event.startDate).toLocaleTimeString()}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <svg className="w-5 h-5 text-neutral-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    <div>
                      <p className="text-sm text-neutral-600 dark:text-neutral-400">Location</p>
                      <p className="font-medium">{event.location || 'TBD'}</p>
                    </div>
                  </div>
                </div>
                {event.requirements && (
                  <div>
                    <p className="text-sm text-neutral-600 dark:text-neutral-400 mb-2">Requirements</p>
                    <p className="text-neutral-900 dark:text-neutral-100">
                      {typeof event.requirements === 'string'
                        ? event.requirements
                        : JSON.stringify(event.requirements, null, 2)
                      }
                    </p>
                  </div>
                )}
                {event.prizes && (
                  <div>
                    <p className="text-sm text-neutral-600 dark:text-neutral-400 mb-2">Prizes</p>
                    <p className="text-neutral-900 dark:text-neutral-100">
                      {typeof event.prizes === 'string'
                        ? event.prizes
                        : JSON.stringify(event.prizes, null, 2)
                      }
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Event Media */}
            <Card>
              <CardHeader>
                <CardTitle>Event Media</CardTitle>
              </CardHeader>
              <CardContent>
                <AcademyMediaManager
                  academyId={event.academyId}
                  academyName={event.academy.name}
                  eventId={event.id}
                  canManage={true} // TODO: Add proper permission check
                  className="border-0 p-0"
                />
              </CardContent>
            </Card>

            {/* Participants */}
            {event.participants && event.participants.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Participants</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-4 md:grid-cols-2">
                    {event.participants.map((participant: any) => (
                      <div key={participant.id} className="flex items-center space-x-3 p-3 bg-neutral-50 dark:bg-neutral-800 rounded-lg">
                        <div className="w-10 h-10 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center">
                          {participant.academyMember.user.avatar ? (
                            <img
                              src={participant.academyMember.user.avatar}
                              alt={`${participant.academyMember.user.firstName} ${participant.academyMember.user.lastName}`}
                              className="w-full h-full rounded-full object-cover"
                            />
                          ) : (
                            <span className="text-sm font-medium text-primary-600 dark:text-primary-400">
                              {participant.academyMember.user.firstName[0]}{participant.academyMember.user.lastName[0]}
                            </span>
                          )}
                        </div>
                        <div className="flex-1">
                          <p className="font-medium">
                            {participant.academyMember.user.firstName} {participant.academyMember.user.lastName}
                          </p>
                          <p className="text-sm text-neutral-600 dark:text-neutral-400">
                            {participant.academyMember.position || 'Member'}
                          </p>
                        </div>
                        <span className="px-2 py-1 rounded-full text-xs font-medium bg-neutral-100 text-neutral-800 dark:bg-neutral-700 dark:text-neutral-200">
                          {participant.status}
                        </span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Academy Info */}
            <Card>
              <CardHeader>
                <CardTitle>Academy</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-3">
                  {event.academy.logo && (
                    <img
                      src={event.academy.logo}
                      alt={event.academy.name}
                      className="w-12 h-12 rounded-lg object-cover"
                    />
                  )}
                  <div>
                    <h3 className="font-semibold">{event.academy.name}</h3>
                    <p className="text-sm text-neutral-600 dark:text-neutral-400">
                      {event.academy.sport}
                    </p>
                  </div>
                </div>
                <Link
                  href={`/academies/${event.academy.slug}`}
                  className="mt-4 block w-full bg-primary-600 text-white text-center py-2 rounded-lg hover:bg-primary-700 transition-colors"
                >
                  View Academy
                </Link>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Link
                  href={`/academies/${event.academy.slug}/events`}
                  className="block w-full bg-neutral-100 dark:bg-neutral-800 text-center py-2 rounded-lg hover:bg-neutral-200 dark:hover:bg-neutral-700 transition-colors"
                >
                  All Events
                </Link>
                <Link
                  href={`/academies/${event.academy.slug}/members`}
                  className="block w-full bg-neutral-100 dark:bg-neutral-800 text-center py-2 rounded-lg hover:bg-neutral-200 dark:hover:bg-neutral-700 transition-colors"
                >
                  Academy Members
                </Link>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}

export async function generateMetadata({ params }: AcademyEventPageProps) {
  const { id } = await params
  const eventId = parseInt(id)
  
  if (isNaN(eventId)) {
    return {
      title: 'Event Not Found'
    }
  }

  const result = await getAcademyEvent(eventId)

  if (!result.success || !result.data) {
    return {
      title: 'Event Not Found'
    }
  }

  const event = result.data

  return {
    title: `${event.title} - ${event.academy.name}`,
    description: `${event.description} at ${event.academy.name}. ${event.type} event scheduled for ${new Date(event.startDate).toLocaleDateString()}.`
  }
}
