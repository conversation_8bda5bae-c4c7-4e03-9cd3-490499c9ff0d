import { notFound } from 'next/navigation'
import Link from 'next/link'
import { getAcademyBySlug } from '@/app/actions/academies'
import { AcademyAnalytics } from '@/components/academies/AcademyAnalytics'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'

interface AcademyAnalyticsPageProps {
  params: Promise<{
    slug: string
  }>
}

export default async function AcademyAnalyticsPage({ params }: AcademyAnalyticsPageProps) {
  const { slug } = await params
  
  const result = await getAcademyBySlug(slug)

  if (!result.success || !result.data) {
    notFound()
  }

  const academy = result.data

  return (
    <div className="min-h-screen bg-neutral-50 dark:bg-neutral-900">
      <div className="container mx-auto px-4 py-8">
        {/* Breadcrumb */}
        <nav className="flex items-center space-x-2 text-sm text-neutral-600 dark:text-neutral-400 mb-6">
          <Link href="/academies" className="hover:text-primary-600">
            Academies
          </Link>
          <span>/</span>
          <Link href={`/academies/${academy.slug}`} className="hover:text-primary-600">
            {academy.name}
          </Link>
          <span>/</span>
          <span className="text-neutral-900 dark:text-neutral-100">Analytics</span>
        </nav>

        {/* Header */}
        <div className="bg-gradient-to-r from-primary-600 to-primary-700 rounded-lg p-6 text-white mb-8">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <h1 className="text-3xl font-bold mb-2">Analytics Dashboard</h1>
              <p className="text-primary-100 mb-4">
                Comprehensive insights and performance metrics for {academy.name}
              </p>
              <div className="flex items-center space-x-4">
                <span className="px-3 py-1 bg-white/20 rounded-full text-sm">
                  {academy.sport}
                </span>
                <span className="text-primary-100">
                  {academy.location}
                </span>
              </div>
            </div>
            {academy.logo && (
              <img
                src={academy.logo}
                alt={academy.name}
                className="w-16 h-16 rounded-lg bg-white/20 p-2"
              />
            )}
          </div>
        </div>

        {/* Quick Navigation */}
        <div className="grid gap-4 md:grid-cols-4 mb-8">
          <Link
            href={`/academies/${academy.slug}`}
            className="p-4 bg-white dark:bg-neutral-800 rounded-lg border border-neutral-200 dark:border-neutral-700 hover:border-primary-300 dark:hover:border-primary-600 transition-colors"
          >
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                <svg className="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
                </svg>
              </div>
              <div>
                <h3 className="font-medium text-neutral-900 dark:text-neutral-100">Overview</h3>
                <p className="text-sm text-neutral-600 dark:text-neutral-400">Academy details</p>
              </div>
            </div>
          </Link>

          <Link
            href={`/academies/${academy.slug}/members`}
            className="p-4 bg-white dark:bg-neutral-800 rounded-lg border border-neutral-200 dark:border-neutral-700 hover:border-primary-300 dark:hover:border-primary-600 transition-colors"
          >
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
                <svg className="w-5 h-5 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                </svg>
              </div>
              <div>
                <h3 className="font-medium text-neutral-900 dark:text-neutral-100">Members</h3>
                <p className="text-sm text-neutral-600 dark:text-neutral-400">Member management</p>
              </div>
            </div>
          </Link>

          <Link
            href={`/academies/${academy.slug}/partnerships`}
            className="p-4 bg-white dark:bg-neutral-800 rounded-lg border border-neutral-200 dark:border-neutral-700 hover:border-primary-300 dark:hover:border-primary-600 transition-colors"
          >
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center">
                <svg className="w-5 h-5 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V8a2 2 0 012-2V6" />
                </svg>
              </div>
              <div>
                <h3 className="font-medium text-neutral-900 dark:text-neutral-100">Partnerships</h3>
                <p className="text-sm text-neutral-600 dark:text-neutral-400">Club & company partnerships</p>
              </div>
            </div>
          </Link>

          <div className="p-4 bg-primary-50 dark:bg-primary-900/20 rounded-lg border border-primary-200 dark:border-primary-700">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                <svg className="w-5 h-5 text-primary-600 dark:text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <div>
                <h3 className="font-medium text-neutral-900 dark:text-neutral-100">Analytics</h3>
                <p className="text-sm text-neutral-600 dark:text-neutral-400">Performance insights</p>
              </div>
            </div>
          </div>
        </div>

        {/* Analytics Dashboard */}
        <AcademyAnalytics
          academyId={academy.id}
          academyName={academy.name}
        />

        {/* Additional Insights */}
        <div className="mt-8 grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>Performance Insights</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
                    Training Effectiveness
                  </h4>
                  <p className="text-sm text-blue-700 dark:text-blue-300">
                    Members show consistent improvement across all skill areas. Technical training sessions have the highest attendance rate.
                  </p>
                </div>
                <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <h4 className="font-medium text-green-900 dark:text-green-100 mb-2">
                    Member Engagement
                  </h4>
                  <p className="text-sm text-green-700 dark:text-green-300">
                    High member retention rate with strong participation in academy events and competitions.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Recommendations</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                  <h4 className="font-medium text-yellow-900 dark:text-yellow-100 mb-2">
                    Expand Physical Training
                  </h4>
                  <p className="text-sm text-yellow-700 dark:text-yellow-300">
                    Consider adding more physical conditioning sessions to balance the training program.
                  </p>
                </div>
                <div className="p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                  <h4 className="font-medium text-purple-900 dark:text-purple-100 mb-2">
                    Partnership Opportunities
                  </h4>
                  <p className="text-sm text-purple-700 dark:text-purple-300">
                    Strong performance metrics make the academy attractive for additional sponsorship partnerships.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

export async function generateMetadata({ params }: AcademyAnalyticsPageProps) {
  const { slug } = await params
  
  const result = await getAcademyBySlug(slug)

  if (!result.success || !result.data) {
    return {
      title: 'Academy Not Found'
    }
  }

  const academy = result.data

  return {
    title: `${academy.name} - Analytics Dashboard`,
    description: `Performance analytics and insights for ${academy.name}. Track member progress, training effectiveness, and partnership ROI.`
  }
}
