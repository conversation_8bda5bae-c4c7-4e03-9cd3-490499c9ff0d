import { notFound } from 'next/navigation'
import { getAcademyBySlug } from '@/app/actions/academies'
import { AcademyPartnerships } from '@/components/academies/AcademyPartnerships'
import { getCurrentUser } from '@/lib/auth'

interface PartnershipsPageProps {
  params: Promise<{ slug: string }>
}

export default async function PartnershipsPage({ params }: PartnershipsPageProps) {
  const { slug } = await params
  
  const [academyResult, currentUser] = await Promise.all([
    getAcademyBySlug(slug),
    getCurrentUser()
  ])

  if (!academyResult.success || !academyResult.data) {
    notFound()
  }

  const academy = academyResult.data

  // Check if user can manage partnerships (for now, allow all authenticated users)
  // TODO: Implement proper academy admin/coach authorization
  const canManage = !!currentUser

  return (
    <div className="min-h-screen bg-neutral-50 dark:bg-neutral-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-4 mb-4">
            <div className="w-16 h-16 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
              {academy.logo ? (
                <img
                  src={academy.logo}
                  alt={academy.name}
                  className="w-full h-full rounded-full object-cover"
                />
              ) : (
                <span className="text-2xl font-bold text-white">
                  {academy.name.charAt(0)}
                </span>
              )}
            </div>
            <div>
              <h1 className="text-3xl font-bold text-neutral-900 dark:text-white">
                {academy.name} Partnerships
              </h1>
              <p className="text-neutral-600 dark:text-neutral-400">
                Manage partnerships with clubs, companies, and institutions
              </p>
            </div>
          </div>
        </div>

        {/* Partnerships Component */}
        <AcademyPartnerships 
          academyId={academy.id} 
          canManage={canManage}
        />
      </div>
    </div>
  )
}
