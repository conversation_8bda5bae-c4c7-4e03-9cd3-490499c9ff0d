import { notFound } from 'next/navigation'
import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { getAcademyBySlug } from '@/app/actions/academies'
import { AcademyHero } from '@/components/academies/AcademyHero'
import { AcademyStats } from '@/components/academies/AcademyStats'
import { AcademyAbout } from '@/components/academies/AcademyAbout'
import { AcademyMembers } from '@/components/academies/AcademyMembers'
import { AcademyTrainings } from '@/components/academies/AcademyTrainings'
import { AcademyEvents } from '@/components/academies/AcademyEvents'
import { AcademyRegistration } from '@/components/academies/AcademyRegistration'

interface AcademyPageProps {
  params: Promise<{
    slug: string
  }>
}

export default async function AcademyPage({ params }: AcademyPageProps) {
  const { slug } = await params
  const result = await getAcademyBySlug(slug)

  if (!result.success || !result.data) {
    notFound()
  }

  const academy = result.data
  const content = academy.content as any

  return (
    <div className="min-h-screen bg-neutral-50 dark:bg-neutral-900">
      {/* Hero Section */}
      <AcademyHero academy={academy} content={content} />

      {/* Navigation */}
      <div className="bg-white dark:bg-neutral-800 border-b border-neutral-200 dark:border-neutral-700 sticky top-0 z-40">
        <div className="container mx-auto px-4">
          <nav className="flex space-x-8 overflow-x-auto">
            <a
              href={`#about`}
              className="py-4 px-2 border-b-2 border-transparent hover:border-primary-500 text-neutral-600 dark:text-neutral-400 hover:text-primary-600 dark:hover:text-primary-400 whitespace-nowrap transition-colors"
            >
              About
            </a>
            <a
              href={`#members`}
              className="py-4 px-2 border-b-2 border-transparent hover:border-primary-500 text-neutral-600 dark:text-neutral-400 hover:text-primary-600 dark:hover:text-primary-400 whitespace-nowrap transition-colors"
            >
              Members
            </a>
            <a
              href={`#trainings`}
              className="py-4 px-2 border-b-2 border-transparent hover:border-primary-500 text-neutral-600 dark:text-neutral-400 hover:text-primary-600 dark:hover:text-primary-400 whitespace-nowrap transition-colors"
            >
              Trainings
            </a>
            <a
              href={`#events`}
              className="py-4 px-2 border-b-2 border-transparent hover:border-primary-500 text-neutral-600 dark:text-neutral-400 hover:text-primary-600 dark:hover:text-primary-400 whitespace-nowrap transition-colors"
            >
              Events
            </a>
            <Link
              href={`/academies/${academy.slug}/media`}
              className="py-4 px-2 border-b-2 border-transparent hover:border-primary-500 text-neutral-600 dark:text-neutral-400 hover:text-primary-600 dark:hover:text-primary-400 whitespace-nowrap transition-colors"
            >
              Media Gallery
            </Link>
            <Link
              href={`/academies/${academy.slug}/partnerships`}
              className="py-4 px-2 border-b-2 border-transparent hover:border-primary-500 text-neutral-600 dark:text-neutral-400 hover:text-primary-600 dark:hover:text-primary-400 whitespace-nowrap transition-colors"
            >
              Partnerships
            </Link>
          </nav>
        </div>
      </div>

      {/* Stats Section */}
      <AcademyStats academy={academy} content={content} />

      <div className="container-mobile space-y-8 py-8">
        {/* About Section */}
        <div id="about">
          <AcademyAbout academy={academy} content={content} />
        </div>

        {/* Registration Section */}
        <AcademyRegistration academy={academy} />

        {/* Members Section */}
        <div id="members">
          <AcademyMembers academy={academy} />
        </div>

        {/* Upcoming Trainings */}
        <div id="trainings">
          <AcademyTrainings academy={academy} />
        </div>

        {/* Upcoming Events */}
        <div id="events">
          <AcademyEvents academy={academy} />
        </div>

        {/* Partnerships */}
        {academy.partnerships && academy.partnerships.length > 0 && (
          <section className="space-y-6">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100 md:text-3xl">
                Our Partners
              </h2>
              <p className="mt-2 text-neutral-600 dark:text-neutral-400">
                Proud to partner with leading clubs and organizations
              </p>
            </div>

            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {academy.partnerships.map((partnership) => (
                <Card key={partnership.id} variant="elevated">
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <span className="text-lg">🤝</span>
                      <span className="capitalize">{partnership.partnershipType}</span>
                    </CardTitle>
                    <CardDescription>
                      {partnership.partnerType} Partnership
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-neutral-600 dark:text-neutral-400">Status:</span>
                        <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                          partnership.status === 'active' 
                            ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-200'
                            : 'bg-neutral-100 text-neutral-800 dark:bg-neutral-900/30 dark:text-neutral-200'
                        }`}>
                          {partnership.status}
                        </span>
                      </div>
                      {partnership.endDate && (
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-neutral-600 dark:text-neutral-400">Until:</span>
                          <span className="text-sm">
                            {new Date(partnership.endDate).toLocaleDateString()}
                          </span>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </section>
        )}

        {/* Contact Information */}
        <section className="space-y-6">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100 md:text-3xl">
              Get In Touch
            </h2>
            <p className="mt-2 text-neutral-600 dark:text-neutral-400">
              Ready to join {academy.name}? Contact us today
            </p>
          </div>

          <div className="grid gap-6 md:grid-cols-2">
            <Card variant="elevated">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <span>📧</span>
                  <span>Email Us</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-neutral-600 dark:text-neutral-400 mb-4">
                  Send us an email for inquiries about membership, training programs, or partnerships.
                </p>
                <a 
                  href={`mailto:${academy.contactEmail}`}
                  className="text-primary-600 dark:text-primary-400 hover:underline"
                >
                  {academy.contactEmail}
                </a>
              </CardContent>
            </Card>

            <Card variant="elevated">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <span>📞</span>
                  <span>Call Us</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-neutral-600 dark:text-neutral-400 mb-4">
                  Speak directly with our team about training schedules and membership options.
                </p>
                <a 
                  href={`tel:${academy.contactPhone}`}
                  className="text-primary-600 dark:text-primary-400 hover:underline"
                >
                  {academy.contactPhone}
                </a>
              </CardContent>
            </Card>
          </div>
        </section>

        {/* Location */}
        <section className="space-y-6">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100 md:text-3xl">
              Visit Our Academy
            </h2>
            <p className="mt-2 text-neutral-600 dark:text-neutral-400">
              Come see our facilities and meet our coaching staff
            </p>
          </div>

          <Card variant="elevated">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <span>📍</span>
                <span>Location</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-lg font-medium text-neutral-900 dark:text-neutral-100">
                  {academy.location}
                </p>
                
                {content?.address && (
                  <p className="text-neutral-600 dark:text-neutral-400">
                    {content.address}
                  </p>
                )}

                <div className="flex flex-col gap-2 sm:flex-row">
                  <Button className="flex-1">
                    Get Directions
                  </Button>
                  <Button variant="outline" className="flex-1">
                    Schedule Visit
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </section>
      </div>
    </div>
  )
}
