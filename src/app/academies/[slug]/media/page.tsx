import { notFound } from 'next/navigation'
import Link from 'next/link'
import { getAcademyBySlug } from '@/app/actions/academies'
import { AcademyMediaManager } from '@/components/academies/AcademyMediaManager'

interface AcademyMediaPageProps {
  params: Promise<{
    slug: string
  }>
}

export default async function AcademyMediaPage({ params }: AcademyMediaPageProps) {
  const { slug } = await params
  
  const result = await getAcademyBySlug(slug)

  if (!result.success || !result.data) {
    notFound()
  }

  const academy = result.data

  return (
    <div className="min-h-screen bg-neutral-50 dark:bg-neutral-900">
      <div className="container mx-auto px-4 py-8">
        {/* Breadcrumb */}
        <nav className="flex items-center space-x-2 text-sm text-neutral-600 dark:text-neutral-400 mb-6">
          <Link href="/academies" className="hover:text-primary-600 dark:hover:text-primary-400">
            Academies
          </Link>
          <span>›</span>
          <Link
            href={`/academies/${academy.slug}`}
            className="hover:text-primary-600 dark:hover:text-primary-400"
          >
            {academy.name}
          </Link>
          <span>›</span>
          <span className="text-neutral-900 dark:text-neutral-100">Media Gallery</span>
        </nav>

        {/* Academy Header */}
        <div className="bg-gradient-to-r from-primary-600 to-primary-700 rounded-lg p-6 text-white mb-8">
          <div className="flex items-center space-x-4">
            {academy.logo && (
              <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center overflow-hidden">
                <img
                  src={academy.logo}
                  alt={academy.name}
                  className="w-full h-full object-cover"
                />
              </div>
            )}
            <div className="flex-1">
              <h1 className="text-3xl font-bold mb-2">{academy.name}</h1>
              <div className="flex items-center space-x-4 text-white/80">
                <span className="capitalize">{academy.sport}</span>
                <span>•</span>
                <span className="capitalize">{academy.level}</span>
                <span>•</span>
                <span>{academy.location}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Media Manager */}
        <AcademyMediaManager
          academyId={academy.id}
          academyName={academy.name}
          canManage={true} // TODO: Add proper permission check based on user role
        />
      </div>
    </div>
  )
}

export async function generateMetadata({ params }: AcademyMediaPageProps) {
  const { slug } = await params
  
  const result = await getAcademyBySlug(slug)

  if (!result.success || !result.data) {
    return {
      title: 'Academy Not Found',
      description: 'The requested academy could not be found.'
    }
  }

  const academy = result.data

  return {
    title: `${academy.name} - Media Gallery | Glbiashara Solution`,
    description: `Browse and manage media for ${academy.name}, a ${academy.sport} academy in ${academy.location}. View talent videos, achievements, training sessions, and event photos.`,
    keywords: [
      academy.name,
      academy.sport,
      'academy media',
      'talent videos',
      'achievements',
      'training',
      'events',
      academy.location,
      'Tanzania sports'
    ].join(', ')
  }
}
