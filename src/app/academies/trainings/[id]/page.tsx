import { notFound } from 'next/navigation'
import Link from 'next/link'
import { getTrainingSession } from '@/app/actions/academies'
import { AcademyMediaManager } from '@/components/academies/AcademyMediaManager'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'

interface TrainingSessionPageProps {
  params: Promise<{
    id: string
  }>
}

export default async function TrainingSessionPage({ params }: TrainingSessionPageProps) {
  const { id } = await params
  const trainingId = parseInt(id)
  
  if (isNaN(trainingId)) {
    notFound()
  }

  const result = await getTrainingSession(trainingId)

  if (!result.success || !result.data) {
    notFound()
  }

  const training = result.data

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
      case 'ongoing':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      case 'completed':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
      case 'cancelled':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
    }
  }

  return (
    <div className="min-h-screen bg-neutral-50 dark:bg-neutral-900">
      <div className="container mx-auto px-4 py-8">
        {/* Breadcrumb */}
        <nav className="flex items-center space-x-2 text-sm text-neutral-600 dark:text-neutral-400 mb-6">
          <Link href="/academies" className="hover:text-primary-600">
            Academies
          </Link>
          <span>/</span>
          <Link href={`/academies/${training.academy.slug}`} className="hover:text-primary-600">
            {training.academy.name}
          </Link>
          <span>/</span>
          <span className="text-neutral-900 dark:text-neutral-100">Training Session</span>
        </nav>

        {/* Header */}
        <div className="bg-gradient-to-r from-primary-600 to-primary-700 rounded-lg p-6 text-white mb-8">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <h1 className="text-3xl font-bold mb-2">{training.title}</h1>
              <p className="text-primary-100 mb-4">{training.description}</p>
              <div className="flex items-center space-x-4">
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(training.status)}`}>
                  {training.status.charAt(0).toUpperCase() + training.status.slice(1)}
                </span>
                <span className="text-primary-100">
                  {training.type.charAt(0).toUpperCase() + training.type.slice(1)} Training
                </span>
              </div>
            </div>
            {training.academy.logo && (
              <img
                src={training.academy.logo}
                alt={training.academy.name}
                className="w-16 h-16 rounded-lg bg-white/20 p-2"
              />
            )}
          </div>
        </div>

        <div className="grid gap-8 lg:grid-cols-3">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Training Details */}
            <Card>
              <CardHeader>
                <CardTitle>Training Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="flex items-center space-x-2">
                    <svg className="w-5 h-5 text-neutral-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 4h6m-6 4h6m-6 4h6" />
                    </svg>
                    <div>
                      <p className="text-sm text-neutral-600 dark:text-neutral-400">Date</p>
                      <p className="font-medium">
                        {new Date(training.scheduledAt).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <svg className="w-5 h-5 text-neutral-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <div>
                      <p className="text-sm text-neutral-600 dark:text-neutral-400">Time</p>
                      <p className="font-medium">
                        {new Date(training.scheduledAt).toLocaleTimeString()} - {training.duration} min
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <svg className="w-5 h-5 text-neutral-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    <div>
                      <p className="text-sm text-neutral-600 dark:text-neutral-400">Location</p>
                      <p className="font-medium">{training.location || 'TBD'}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <svg className="w-5 h-5 text-neutral-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                    </svg>
                    <div>
                      <p className="text-sm text-neutral-600 dark:text-neutral-400">Participants</p>
                      <p className="font-medium">{training.maxParticipants || 'Unlimited'}</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Training Media */}
            <Card>
              <CardHeader>
                <CardTitle>Training Media</CardTitle>
              </CardHeader>
              <CardContent>
                <AcademyMediaManager
                  academyId={training.academyId}
                  academyName={training.academy.name}
                  trainingId={training.id}
                  canManage={true} // TODO: Add proper permission check
                  className="border-0 p-0"
                />
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Academy Info */}
            <Card>
              <CardHeader>
                <CardTitle>Academy</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-3">
                  {training.academy.logo && (
                    <img
                      src={training.academy.logo}
                      alt={training.academy.name}
                      className="w-12 h-12 rounded-lg object-cover"
                    />
                  )}
                  <div>
                    <h3 className="font-semibold">{training.academy.name}</h3>
                    <p className="text-sm text-neutral-600 dark:text-neutral-400">
                      {training.academy.sport}
                    </p>
                  </div>
                </div>
                <Link
                  href={`/academies/${training.academy.slug}`}
                  className="mt-4 block w-full bg-primary-600 text-white text-center py-2 rounded-lg hover:bg-primary-700 transition-colors"
                >
                  View Academy
                </Link>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Link
                  href={`/academies/${training.academy.slug}/trainings`}
                  className="block w-full bg-neutral-100 dark:bg-neutral-800 text-center py-2 rounded-lg hover:bg-neutral-200 dark:hover:bg-neutral-700 transition-colors"
                >
                  All Training Sessions
                </Link>
                <Link
                  href={`/academies/${training.academy.slug}/members`}
                  className="block w-full bg-neutral-100 dark:bg-neutral-800 text-center py-2 rounded-lg hover:bg-neutral-200 dark:hover:bg-neutral-700 transition-colors"
                >
                  Academy Members
                </Link>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}

export async function generateMetadata({ params }: TrainingSessionPageProps) {
  const { id } = await params
  const trainingId = parseInt(id)
  
  if (isNaN(trainingId)) {
    return {
      title: 'Training Session Not Found'
    }
  }

  const result = await getTrainingSession(trainingId)

  if (!result.success || !result.data) {
    return {
      title: 'Training Session Not Found'
    }
  }

  const training = result.data

  return {
    title: `${training.title} - ${training.academy.name}`,
    description: `${training.description} at ${training.academy.name}. Scheduled for ${new Date(training.scheduledAt).toLocaleDateString()}.`
  }
}
