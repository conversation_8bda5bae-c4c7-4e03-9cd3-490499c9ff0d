import Link from 'next/link'
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/Card'
import { But<PERSON> } from '@/components/ui/Button'

export default function AcademyRegisterPage() {
  return (
    <div className="min-h-screen bg-neutral-50 dark:bg-neutral-900">
      <div className="container mx-auto px-4 py-8">
        {/* Breadcrumb */}
        <nav className="flex items-center space-x-2 text-sm text-neutral-600 dark:text-neutral-400 mb-6">
          <Link href="/academies" className="hover:text-primary-600">
            Academies
          </Link>
          <span>/</span>
          <span className="text-neutral-900 dark:text-neutral-100">Register</span>
        </nav>

        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-neutral-900 dark:text-neutral-100 mb-2">
            Register Your Sports Academy
          </h1>
          <p className="text-neutral-600 dark:text-neutral-400 max-w-2xl mx-auto">
            Join our platform to manage your academy, track member performance, and connect with clubs and sponsors.
          </p>
        </div>

        {/* Registration Steps */}
        <div className="max-w-4xl mx-auto">
          <div className="grid gap-6 md:grid-cols-3 mb-8">
            <Card className="text-center">
              <CardHeader>
                <div className="w-12 h-12 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mx-auto mb-2">
                  <span className="text-xl font-bold text-primary-600 dark:text-primary-400">1</span>
                </div>
                <CardTitle className="text-lg">Academy Details</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-neutral-600 dark:text-neutral-400">
                  Provide basic information about your academy, including name, sport, location, and contact details.
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <div className="w-12 h-12 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mx-auto mb-2">
                  <span className="text-xl font-bold text-primary-600 dark:text-primary-400">2</span>
                </div>
                <CardTitle className="text-lg">Profile Setup</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-neutral-600 dark:text-neutral-400">
                  Upload your logo, add description, facilities information, and training programs offered.
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <div className="w-12 h-12 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mx-auto mb-2">
                  <span className="text-xl font-bold text-primary-600 dark:text-primary-400">3</span>
                </div>
                <CardTitle className="text-lg">Go Live</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-neutral-600 dark:text-neutral-400">
                  Review and publish your academy profile to start accepting member registrations.
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Benefits Section */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle>Why Register Your Academy?</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-6 md:grid-cols-2">
                <div>
                  <h4 className="font-semibold text-neutral-900 dark:text-neutral-100 mb-3">
                    Member Management
                  </h4>
                  <ul className="space-y-2 text-sm text-neutral-600 dark:text-neutral-400">
                    <li className="flex items-center">
                      <svg className="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      Online member registration system
                    </li>
                    <li className="flex items-center">
                      <svg className="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      Performance tracking and analytics
                    </li>
                    <li className="flex items-center">
                      <svg className="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      Digital member profiles and portfolios
                    </li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold text-neutral-900 dark:text-neutral-100 mb-3">
                    Growth Opportunities
                  </h4>
                  <ul className="space-y-2 text-sm text-neutral-600 dark:text-neutral-400">
                    <li className="flex items-center">
                      <svg className="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      Connect with professional clubs
                    </li>
                    <li className="flex items-center">
                      <svg className="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      Partnership with sponsors and companies
                    </li>
                    <li className="flex items-center">
                      <svg className="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      Increased visibility and reach
                    </li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Call to Action */}
          <div className="text-center">
            <Card className="bg-gradient-to-r from-primary-50 to-accent-50 dark:from-primary-900/20 dark:to-accent-900/20 border-primary-200 dark:border-primary-800">
              <CardContent className="p-8">
                <h3 className="text-xl font-bold text-neutral-900 dark:text-neutral-100 mb-2">
                  Ready to Get Started?
                </h3>
                <p className="text-neutral-600 dark:text-neutral-400 mb-6">
                  Registration is free and takes less than 10 minutes. Join hundreds of academies already on our platform.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button size="lg" className="px-8">
                    Start Registration
                  </Button>
                  <Button size="lg" variant="outline">
                    Schedule Demo
                  </Button>
                </div>
                <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-4">
                  Need help? Contact our support <NAME_EMAIL>
                </p>
              </CardContent>
            </Card>
          </div>

          {/* FAQ Section */}
          <div className="mt-12">
            <h3 className="text-xl font-bold text-neutral-900 dark:text-neutral-100 mb-6 text-center">
              Frequently Asked Questions
            </h3>
            <div className="grid gap-4 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Is registration free?</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-neutral-600 dark:text-neutral-400">
                    Yes, academy registration is completely free. We only charge a small commission on successful partnerships and sponsorship deals.
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-base">What sports are supported?</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-neutral-600 dark:text-neutral-400">
                    We support all sports including football, basketball, volleyball, athletics, swimming, and many more. If your sport isn't listed, contact us.
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-base">How do I manage members?</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-neutral-600 dark:text-neutral-400">
                    Our platform provides a complete member management system with registration, performance tracking, and communication tools.
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Can I customize my academy profile?</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-neutral-600 dark:text-neutral-400">
                    Absolutely! You can upload your logo, add photos, customize descriptions, and showcase your facilities and achievements.
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export async function generateMetadata() {
  return {
    title: 'Register Your Sports Academy - Glbiashara',
    description: 'Join our platform to manage your sports academy, track member performance, and connect with clubs and sponsors. Free registration for all academies.'
  }
}
