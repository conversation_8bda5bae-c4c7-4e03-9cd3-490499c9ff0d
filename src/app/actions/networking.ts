'use server'

import { prisma } from '@/lib/prisma'
import { revalidatePath } from 'next/cache'

// ========================================
// INTERNATIONAL NETWORKS
// ========================================

export async function createInternationalNetwork(data: {
  userId: number
  networkType: string
  title: string
  description: string
  countries: string[]
  objectives: string[]
  activities: any
  membershipType: string
}) {
  try {
    const network = await prisma.internationalNetwork.create({
      data,
      include: {
        creator: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            avatar: true
          }
        },
        _count: {
          select: {
            memberships: true,
            events: true,
            exchanges: true
          }
        }
      }
    })

    revalidatePath('/networking')
    return { success: true, network }
  } catch (error) {
    console.error('Error creating international network:', error)
    return { success: false, error: 'Failed to create international network' }
  }
}

export async function getInternationalNetworks(filters?: {
  networkType?: string
  country?: string
  membershipType?: string
}) {
  try {
    const networks = await prisma.internationalNetwork.findMany({
      where: {
        isActive: true,
        ...(filters?.networkType && { networkType: filters.networkType }),
        ...(filters?.country && { countries: { has: filters.country } }),
        ...(filters?.membershipType && { membershipType: filters.membershipType })
      },
      include: {
        creator: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            avatar: true
          }
        },
        _count: {
          select: {
            memberships: true,
            events: true,
            exchanges: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    return { success: true, networks }
  } catch (error) {
    console.error('Error fetching international networks:', error)
    return { success: false, error: 'Failed to fetch networks' }
  }
}

export async function joinNetwork(data: {
  networkId: number
  userId: number
  role: string
  expertise: string[]
  interests: string[]
}) {
  try {
    const membership = await prisma.networkMembership.create({
      data: {
        ...data,
        status: 'pending'
      },
      include: {
        network: {
          select: {
            id: true,
            title: true,
            networkType: true
          }
        },
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        }
      }
    })

    revalidatePath('/networking')
    return { success: true, membership }
  } catch (error) {
    console.error('Error joining network:', error)
    return { success: false, error: 'Failed to join network' }
  }
}

export async function updateMembershipStatus(data: {
  membershipId: number
  status: string
  role?: string
}) {
  try {
    const membership = await prisma.networkMembership.update({
      where: { id: data.membershipId },
      data: {
        status: data.status,
        ...(data.role && { role: data.role })
      },
      include: {
        network: {
          select: {
            id: true,
            title: true
          }
        },
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        }
      }
    })

    revalidatePath('/networking')
    return { success: true, membership }
  } catch (error) {
    console.error('Error updating membership status:', error)
    return { success: false, error: 'Failed to update membership' }
  }
}

// ========================================
// DIPLOMATIC EVENTS
// ========================================

export async function createDiplomaticEvent(data: {
  networkId: number
  organizerId: number
  title: string
  description: string
  type: string
  startDate: Date
  endDate: Date
  location: string
  countries: string[]
  objectives: string[]
  expectedOutcomes: string[]
  agenda: any
  mediaUrls: string[]
  isPublic: boolean
}) {
  try {
    const event = await prisma.diplomaticEvent.create({
      data: {
        ...data,
        status: 'planned'
      },
      include: {
        network: {
          select: {
            id: true,
            title: true,
            networkType: true
          }
        },
        organizer: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        }
      }
    })

    revalidatePath('/networking')
    return { success: true, event }
  } catch (error) {
    console.error('Error creating diplomatic event:', error)
    return { success: false, error: 'Failed to create diplomatic event' }
  }
}

export async function getDiplomaticEvents(filters?: {
  networkId?: number
  type?: string
  country?: string
  status?: string
}) {
  try {
    const events = await prisma.diplomaticEvent.findMany({
      where: {
        ...(filters?.networkId && { networkId: filters.networkId }),
        ...(filters?.type && { type: filters.type }),
        ...(filters?.country && { countries: { has: filters.country } }),
        ...(filters?.status && { status: filters.status }),
        isPublic: true
      },
      include: {
        network: {
          select: {
            id: true,
            title: true,
            networkType: true
          }
        },
        organizer: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            avatar: true
          }
        }
      },
      orderBy: {
        startDate: 'asc'
      }
    })

    return { success: true, events }
  } catch (error) {
    console.error('Error fetching diplomatic events:', error)
    return { success: false, error: 'Failed to fetch events' }
  }
}

export async function updateEventStatus(data: {
  eventId: number
  status: string
  outcomes?: any
}) {
  try {
    const event = await prisma.diplomaticEvent.update({
      where: { id: data.eventId },
      data: {
        status: data.status,
        ...(data.outcomes && { outcomes: data.outcomes })
      },
      include: {
        network: {
          select: {
            id: true,
            title: true
          }
        }
      }
    })

    revalidatePath('/networking')
    return { success: true, event }
  } catch (error) {
    console.error('Error updating event status:', error)
    return { success: false, error: 'Failed to update event' }
  }
}

// ========================================
// CULTURAL EXCHANGES
// ========================================

export async function createCulturalExchange(data: {
  networkId: number
  coordinatorId: number
  title: string
  description: string
  type: string
  duration: number
  sourceCountry: string
  targetCountry: string
  maxParticipants: number
  requirements: any
  benefits: string[]
  curriculum?: any
  cost?: number
  currency?: string
  applicationDeadline?: Date
  startDate: Date
  endDate: Date
}) {
  try {
    const exchange = await prisma.culturalExchange.create({
      data: {
        ...data,
        status: 'open'
      },
      include: {
        network: {
          select: {
            id: true,
            title: true,
            networkType: true
          }
        },
        coordinator: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        },
        _count: {
          select: {
            applications: true
          }
        }
      }
    })

    revalidatePath('/networking')
    return { success: true, exchange }
  } catch (error) {
    console.error('Error creating cultural exchange:', error)
    return { success: false, error: 'Failed to create cultural exchange' }
  }
}

export async function getCulturalExchanges(filters?: {
  networkId?: number
  type?: string
  sourceCountry?: string
  targetCountry?: string
  status?: string
}) {
  try {
    const exchanges = await prisma.culturalExchange.findMany({
      where: {
        ...(filters?.networkId && { networkId: filters.networkId }),
        ...(filters?.type && { type: filters.type }),
        ...(filters?.sourceCountry && { sourceCountry: filters.sourceCountry }),
        ...(filters?.targetCountry && { targetCountry: filters.targetCountry }),
        ...(filters?.status && { status: filters.status })
      },
      include: {
        network: {
          select: {
            id: true,
            title: true,
            networkType: true
          }
        },
        coordinator: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            avatar: true
          }
        },
        _count: {
          select: {
            applications: true
          }
        }
      },
      orderBy: {
        startDate: 'asc'
      }
    })

    return { success: true, exchanges }
  } catch (error) {
    console.error('Error fetching cultural exchanges:', error)
    return { success: false, error: 'Failed to fetch exchanges' }
  }
}

export async function applyForExchange(data: {
  exchangeId: number
  applicantId: number
  motivation: string
  experience: any
  references: any
  documents: string[]
}) {
  try {
    const application = await prisma.exchangeApplication.create({
      data: {
        ...data,
        status: 'submitted'
      },
      include: {
        exchange: {
          include: {
            network: {
              select: {
                id: true,
                title: true
              }
            },
            coordinator: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true
              }
            }
          }
        },
        applicant: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        }
      }
    })

    revalidatePath('/networking')
    return { success: true, application }
  } catch (error) {
    console.error('Error applying for exchange:', error)
    return { success: false, error: 'Failed to submit application' }
  }
}

export async function updateExchangeApplication(data: {
  applicationId: number
  status: string
  feedback?: string
}) {
  try {
    const application = await prisma.exchangeApplication.update({
      where: { id: data.applicationId },
      data: {
        status: data.status,
        ...(data.feedback && { feedback: data.feedback }),
        reviewedAt: new Date()
      },
      include: {
        exchange: {
          select: {
            id: true,
            title: true
          }
        },
        applicant: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        }
      }
    })

    revalidatePath('/networking')
    return { success: true, application }
  } catch (error) {
    console.error('Error updating exchange application:', error)
    return { success: false, error: 'Failed to update application' }
  }
}

// ========================================
// NETWORKING ANALYTICS
// ========================================

export async function getNetworkingAnalytics(userId: number) {
  try {
    const [
      networksCreated,
      networkMemberships,
      eventsOrganized,
      exchangesCoordinated,
      applications
    ] = await Promise.all([
      prisma.internationalNetwork.count({
        where: { userId, isActive: true }
      }),
      prisma.networkMembership.count({
        where: { userId, status: 'active' }
      }),
      prisma.diplomaticEvent.count({
        where: { organizerId: userId }
      }),
      prisma.culturalExchange.count({
        where: { coordinatorId: userId }
      }),
      prisma.exchangeApplication.count({
        where: { applicantId: userId }
      })
    ])

    return {
      success: true,
      analytics: {
        networksCreated,
        networkMemberships,
        eventsOrganized,
        exchangesCoordinated,
        applications
      }
    }
  } catch (error) {
    console.error('Error fetching networking analytics:', error)
    return { success: false, error: 'Failed to fetch analytics' }
  }
}
