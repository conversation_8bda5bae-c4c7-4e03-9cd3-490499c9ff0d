'use server'

import { prisma } from '@/lib/prisma'
import { revalidatePath } from 'next/cache'

// ========================================
// TECH INNOVATIONS
// ========================================

export async function createTechInnovation(data: {
  userId: number
  academyId?: number
  institutionId?: number
  title: string
  description: string
  category: string
  stage: string
  techStack: string[]
  features: any
  benefits: string[]
  targetUsers: string[]
  businessModel?: any
  mediaUrls: string[]
  githubUrl?: string
  demoUrl?: string
  isOpenSource: boolean
  isCommercial: boolean
}) {
  try {
    const innovation = await prisma.techInnovation.create({
      data,
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            avatar: true
          }
        },
        academy: {
          select: {
            id: true,
            name: true,
            logo: true,
            slug: true
          }
        },
        institution: {
          select: {
            id: true,
            name: true,
            logo: true,
            slug: true
          }
        },
        _count: {
          select: {
            adoptions: true
          }
        }
      }
    })

    revalidatePath('/technology')
    return { success: true, innovation }
  } catch (error) {
    console.error('Error creating tech innovation:', error)
    return { success: false, error: 'Failed to create tech innovation' }
  }
}

export async function getTechInnovations(filters?: {
  category?: string
  stage?: string
  isOpenSource?: boolean
  isCommercial?: boolean
}) {
  try {
    const innovations = await prisma.techInnovation.findMany({
      where: {
        ...(filters?.category && { category: filters.category }),
        ...(filters?.stage && { stage: filters.stage }),
        ...(filters?.isOpenSource !== undefined && { isOpenSource: filters.isOpenSource }),
        ...(filters?.isCommercial !== undefined && { isCommercial: filters.isCommercial })
      },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            avatar: true
          }
        },
        academy: {
          select: {
            id: true,
            name: true,
            logo: true
          }
        },
        institution: {
          select: {
            id: true,
            name: true,
            logo: true
          }
        },
        _count: {
          select: {
            adoptions: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    return { success: true, innovations }
  } catch (error) {
    console.error('Error fetching tech innovations:', error)
    return { success: false, error: 'Failed to fetch tech innovations' }
  }
}

export async function adoptTechnology(data: {
  innovationId: number
  adopterId: number
  adopterType: string
  feedback?: string
}) {
  try {
    const adoption = await prisma.techAdoption.create({
      data: {
        ...data,
        status: 'interested'
      },
      include: {
        innovation: {
          select: {
            id: true,
            title: true,
            category: true
          }
        },
        adopter: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        }
      }
    })

    revalidatePath('/technology')
    return { success: true, adoption }
  } catch (error) {
    console.error('Error adopting technology:', error)
    return { success: false, error: 'Failed to adopt technology' }
  }
}

export async function updateAdoptionStatus(data: {
  adoptionId: number
  status: string
  feedback?: string
  metrics?: any
}) {
  try {
    const adoption = await prisma.techAdoption.update({
      where: { id: data.adoptionId },
      data: {
        status: data.status,
        ...(data.feedback && { feedback: data.feedback }),
        ...(data.metrics && { metrics: data.metrics })
      },
      include: {
        innovation: {
          select: {
            id: true,
            title: true
          }
        },
        adopter: {
          select: {
            id: true,
            firstName: true,
            lastName: true
          }
        }
      }
    })

    revalidatePath('/technology')
    return { success: true, adoption }
  } catch (error) {
    console.error('Error updating adoption status:', error)
    return { success: false, error: 'Failed to update adoption' }
  }
}

// ========================================
// INFRASTRUCTURE PROJECTS
// ========================================

export async function createInfrastructure(data: {
  userId: number
  academyId?: number
  title: string
  description: string
  type: string
  location: string
  scope: any
  budget: number
  currency: string
  timeline: any
  sustainability?: any
  technology?: any
  community?: any
  mediaUrls: string[]
}) {
  try {
    const infrastructure = await prisma.infrastructure.create({
      data: {
        ...data,
        fundingStatus: 'seeking',
        status: 'planning'
      },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            avatar: true
          }
        },
        academy: {
          select: {
            id: true,
            name: true,
            logo: true
          }
        },
        _count: {
          select: {
            updates: true
          }
        }
      }
    })

    revalidatePath('/technology')
    return { success: true, infrastructure }
  } catch (error) {
    console.error('Error creating infrastructure project:', error)
    return { success: false, error: 'Failed to create infrastructure project' }
  }
}

export async function getInfrastructureProjects(filters?: {
  type?: string
  status?: string
  fundingStatus?: string
  location?: string
}) {
  try {
    const projects = await prisma.infrastructure.findMany({
      where: {
        ...(filters?.type && { type: filters.type }),
        ...(filters?.status && { status: filters.status }),
        ...(filters?.fundingStatus && { fundingStatus: filters.fundingStatus }),
        ...(filters?.location && { location: { contains: filters.location, mode: 'insensitive' } })
      },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            avatar: true
          }
        },
        academy: {
          select: {
            id: true,
            name: true,
            logo: true
          }
        },
        _count: {
          select: {
            updates: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    return { success: true, projects }
  } catch (error) {
    console.error('Error fetching infrastructure projects:', error)
    return { success: false, error: 'Failed to fetch infrastructure projects' }
  }
}

export async function updateInfrastructureStatus(data: {
  infrastructureId: number
  status: string
  fundingStatus?: string
}) {
  try {
    const infrastructure = await prisma.infrastructure.update({
      where: { id: data.infrastructureId },
      data: {
        status: data.status,
        ...(data.fundingStatus && { fundingStatus: data.fundingStatus })
      },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true
          }
        }
      }
    })

    revalidatePath('/technology')
    return { success: true, infrastructure }
  } catch (error) {
    console.error('Error updating infrastructure status:', error)
    return { success: false, error: 'Failed to update infrastructure' }
  }
}

export async function createInfrastructureUpdate(data: {
  infrastructureId: number
  title: string
  content: string
  type: string
  progress: number
  budget?: any
  timeline?: any
  challenges: string[]
  mediaUrls: string[]
  isPublic: boolean
}) {
  try {
    const update = await prisma.infrastructureUpdate.create({
      data,
      include: {
        infrastructure: {
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true
              }
            }
          }
        }
      }
    })

    revalidatePath('/technology')
    return { success: true, update }
  } catch (error) {
    console.error('Error creating infrastructure update:', error)
    return { success: false, error: 'Failed to create infrastructure update' }
  }
}

// ========================================
// TECHNOLOGY ANALYTICS
// ========================================

export async function getTechnologyAnalytics(userId: number) {
  try {
    const [
      innovationsCreated,
      technologiesAdopted,
      infrastructureProjects,
      totalAdoptions
    ] = await Promise.all([
      prisma.techInnovation.count({
        where: { userId }
      }),
      prisma.techAdoption.count({
        where: { adopterId: userId }
      }),
      prisma.infrastructure.count({
        where: { userId }
      }),
      prisma.techAdoption.count({
        where: {
          innovation: {
            userId
          }
        }
      })
    ])

    return {
      success: true,
      analytics: {
        innovationsCreated,
        technologiesAdopted,
        infrastructureProjects,
        totalAdoptions
      }
    }
  } catch (error) {
    console.error('Error fetching technology analytics:', error)
    return { success: false, error: 'Failed to fetch analytics' }
  }
}

export async function getInnovationsByCategory() {
  try {
    const innovations = await prisma.techInnovation.groupBy({
      by: ['category'],
      _count: {
        id: true
      },
      orderBy: {
        _count: {
          id: 'desc'
        }
      }
    })

    return { success: true, innovations }
  } catch (error) {
    console.error('Error fetching innovations by category:', error)
    return { success: false, error: 'Failed to fetch innovation categories' }
  }
}

export async function getInfrastructureByType() {
  try {
    const projects = await prisma.infrastructure.groupBy({
      by: ['type'],
      _count: {
        id: true
      },
      _sum: {
        budget: true
      },
      orderBy: {
        _count: {
          id: 'desc'
        }
      }
    })

    return { success: true, projects }
  } catch (error) {
    console.error('Error fetching infrastructure by type:', error)
    return { success: false, error: 'Failed to fetch infrastructure types' }
  }
}

export async function getTechAdoptionMetrics() {
  try {
    const adoptions = await prisma.techAdoption.groupBy({
      by: ['status'],
      _count: {
        id: true
      }
    })

    return { success: true, adoptions }
  } catch (error) {
    console.error('Error fetching tech adoption metrics:', error)
    return { success: false, error: 'Failed to fetch adoption metrics' }
  }
}

export async function getInfrastructureUpdates(infrastructureId: number) {
  try {
    const updates = await prisma.infrastructureUpdate.findMany({
      where: {
        infrastructureId,
        isPublic: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    return { success: true, updates }
  } catch (error) {
    console.error('Error fetching infrastructure updates:', error)
    return { success: false, error: 'Failed to fetch updates' }
  }
}
