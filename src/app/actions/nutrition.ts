'use server'

import { prisma } from '@/lib/prisma'
import { revalidatePath } from 'next/cache'

// ========================================
// NUTRITION PRODUCTS
// ========================================

export async function createNutritionProduct(data: {
  userId: number
  title: string
  description: string
  category: string
  type: string
  ingredients: any
  nutritionFacts: any
  targetAudience: string[]
  certifications: string[]
  price: number
  currency: string
  unit: string
  mediaUrls: string[]
  supplierInfo?: any
}) {
  try {
    const product = await prisma.nutritionProduct.create({
      data,
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            avatar: true
          }
        },
        _count: {
          select: {
            orders: true,
            reviews: true
          }
        }
      }
    })

    revalidatePath('/nutrition')
    return { success: true, product }
  } catch (error) {
    console.error('Error creating nutrition product:', error)
    return { success: false, error: 'Failed to create nutrition product' }
  }
}

export async function getNutritionProducts(filters?: {
  category?: string
  type?: string
  targetAudience?: string
  certification?: string
  minPrice?: number
  maxPrice?: number
}) {
  try {
    const products = await prisma.nutritionProduct.findMany({
      where: {
        isActive: true,
        ...(filters?.category && { category: filters.category }),
        ...(filters?.type && { type: filters.type }),
        ...(filters?.targetAudience && { 
          targetAudience: { has: filters.targetAudience } 
        }),
        ...(filters?.certification && { 
          certifications: { has: filters.certification } 
        }),
        ...(filters?.minPrice && { price: { gte: filters.minPrice } }),
        ...(filters?.maxPrice && { price: { lte: filters.maxPrice } })
      },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            avatar: true
          }
        },
        _count: {
          select: {
            orders: true,
            reviews: true
          }
        },
        reviews: {
          select: {
            rating: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    // Calculate average rating for each product
    const productsWithRating = products.map(product => ({
      ...product,
      averageRating: product.reviews.length > 0 
        ? product.reviews.reduce((sum, review) => sum + review.rating, 0) / product.reviews.length
        : 0
    }))

    return { success: true, products: productsWithRating }
  } catch (error) {
    console.error('Error fetching nutrition products:', error)
    return { success: false, error: 'Failed to fetch nutrition products' }
  }
}

export async function createNutritionOrder(data: {
  buyerId: number
  sellerId: number
  productId: number
  quantity: number
  unitPrice: number
  totalAmount: number
  currency: string
  deliveryAddress: any
  notes?: string
}) {
  try {
    const order = await prisma.nutritionOrder.create({
      data: {
        ...data,
        status: 'pending'
      },
      include: {
        buyer: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        },
        seller: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        },
        product: {
          select: {
            id: true,
            title: true,
            mediaUrls: true
          }
        }
      }
    })

    revalidatePath('/nutrition')
    return { success: true, order }
  } catch (error) {
    console.error('Error creating nutrition order:', error)
    return { success: false, error: 'Failed to create order' }
  }
}

export async function updateOrderStatus(data: {
  orderId: number
  status: string
  deliveryDate?: Date
}) {
  try {
    const order = await prisma.nutritionOrder.update({
      where: { id: data.orderId },
      data: {
        status: data.status,
        ...(data.deliveryDate && { deliveryDate: data.deliveryDate })
      },
      include: {
        buyer: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        },
        product: {
          select: {
            id: true,
            title: true
          }
        }
      }
    })

    revalidatePath('/nutrition')
    return { success: true, order }
  } catch (error) {
    console.error('Error updating order status:', error)
    return { success: false, error: 'Failed to update order' }
  }
}

// ========================================
// SUPPLY CHAIN MANAGEMENT
// ========================================

export async function createSupplyChainLink(data: {
  productId: number
  supplierId: number
  linkType: string
  description: string
  location: string
  processes: any
  quality?: any
  timeline: any
}) {
  try {
    const link = await prisma.supplyChainLink.create({
      data,
      include: {
        product: {
          select: {
            id: true,
            title: true
          }
        },
        supplier: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        }
      }
    })

    revalidatePath('/nutrition')
    return { success: true, link }
  } catch (error) {
    console.error('Error creating supply chain link:', error)
    return { success: false, error: 'Failed to create supply chain link' }
  }
}

export async function getSupplyChain(productId: number) {
  try {
    const links = await prisma.supplyChainLink.findMany({
      where: { productId },
      include: {
        supplier: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            avatar: true
          }
        }
      },
      orderBy: {
        createdAt: 'asc'
      }
    })

    return { success: true, links }
  } catch (error) {
    console.error('Error fetching supply chain:', error)
    return { success: false, error: 'Failed to fetch supply chain' }
  }
}

export async function verifySupplyChainLink(linkId: number) {
  try {
    const link = await prisma.supplyChainLink.update({
      where: { id: linkId },
      data: { isVerified: true },
      include: {
        product: {
          select: {
            id: true,
            title: true
          }
        },
        supplier: {
          select: {
            id: true,
            firstName: true,
            lastName: true
          }
        }
      }
    })

    revalidatePath('/nutrition')
    return { success: true, link }
  } catch (error) {
    console.error('Error verifying supply chain link:', error)
    return { success: false, error: 'Failed to verify link' }
  }
}

// ========================================
// PRODUCT REVIEWS
// ========================================

export async function createProductReview(data: {
  productId: number
  userId: number
  rating: number
  title?: string
  content: string
  mediaUrls: string[]
}) {
  try {
    // Check if user has purchased this product
    const hasPurchased = await prisma.nutritionOrder.findFirst({
      where: {
        buyerId: data.userId,
        productId: data.productId,
        status: 'delivered'
      }
    })

    const review = await prisma.productReview.create({
      data: {
        ...data,
        verified: !!hasPurchased
      },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            avatar: true
          }
        },
        product: {
          select: {
            id: true,
            title: true
          }
        }
      }
    })

    revalidatePath('/nutrition')
    return { success: true, review }
  } catch (error) {
    console.error('Error creating product review:', error)
    return { success: false, error: 'Failed to create review' }
  }
}

export async function getProductReviews(productId: number) {
  try {
    const reviews = await prisma.productReview.findMany({
      where: { productId },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            avatar: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    return { success: true, reviews }
  } catch (error) {
    console.error('Error fetching product reviews:', error)
    return { success: false, error: 'Failed to fetch reviews' }
  }
}

export async function markReviewHelpful(reviewId: number) {
  try {
    const review = await prisma.productReview.update({
      where: { id: reviewId },
      data: {
        helpful: {
          increment: 1
        }
      }
    })

    revalidatePath('/nutrition')
    return { success: true, review }
  } catch (error) {
    console.error('Error marking review helpful:', error)
    return { success: false, error: 'Failed to mark review helpful' }
  }
}

// ========================================
// NUTRITION ANALYTICS
// ========================================

export async function getNutritionAnalytics(userId: number) {
  try {
    const [
      totalProducts,
      totalOrders,
      totalRevenue,
      recentOrders
    ] = await Promise.all([
      prisma.nutritionProduct.count({
        where: { userId, isActive: true }
      }),
      prisma.nutritionOrder.count({
        where: { sellerId: userId }
      }),
      prisma.nutritionOrder.aggregate({
        where: { 
          sellerId: userId,
          status: 'delivered'
        },
        _sum: {
          totalAmount: true
        }
      }),
      prisma.nutritionOrder.findMany({
        where: { sellerId: userId },
        include: {
          buyer: {
            select: {
              id: true,
              firstName: true,
              lastName: true
            }
          },
          product: {
            select: {
              id: true,
              title: true
            }
          }
        },
        orderBy: {
          orderDate: 'desc'
        },
        take: 10
      })
    ])

    return {
      success: true,
      analytics: {
        totalProducts,
        totalOrders,
        totalRevenue: totalRevenue._sum.totalAmount || 0,
        recentOrders
      }
    }
  } catch (error) {
    console.error('Error fetching nutrition analytics:', error)
    return { success: false, error: 'Failed to fetch analytics' }
  }
}
