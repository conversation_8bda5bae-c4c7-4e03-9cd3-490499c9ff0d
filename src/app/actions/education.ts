'use server'

import { prisma } from '@/lib/prisma'
import { revalidatePath } from 'next/cache'

// ========================================
// EDUCATIONAL PROGRAMS
// ========================================

export async function createEducationalProgram(data: {
  academyId?: number
  institutionId?: number
  title: string
  description: string
  category: string
  level: string
  duration: number
  curriculum: any
  prerequisites: string[]
  learningOutcomes: string[]
  mediaUrls: string[]
}) {
  try {
    const program = await prisma.educationalProgram.create({
      data,
      include: {
        academy: true,
        institution: true,
        _count: {
          select: {
            enrollments: true,
            certifications: true
          }
        }
      }
    })

    revalidatePath('/education')
    return { success: true, program }
  } catch (error) {
    console.error('Error creating educational program:', error)
    return { success: false, error: 'Failed to create educational program' }
  }
}

export async function getEducationalPrograms(filters?: {
  category?: string
  level?: string
  academyId?: number
  institutionId?: number
}) {
  try {
    const programs = await prisma.educationalProgram.findMany({
      where: {
        isActive: true,
        ...(filters?.category && { category: filters.category }),
        ...(filters?.level && { level: filters.level }),
        ...(filters?.academyId && { academyId: filters.academyId }),
        ...(filters?.institutionId && { institutionId: filters.institutionId })
      },
      include: {
        academy: {
          select: {
            id: true,
            name: true,
            logo: true,
            slug: true
          }
        },
        institution: {
          select: {
            id: true,
            name: true,
            logo: true,
            slug: true
          }
        },
        _count: {
          select: {
            enrollments: true,
            certifications: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    return { success: true, programs }
  } catch (error) {
    console.error('Error fetching educational programs:', error)
    return { success: false, error: 'Failed to fetch educational programs' }
  }
}

export async function enrollInProgram(data: {
  userId: number
  programId: number
  academyMemberId?: number
}) {
  try {
    const enrollment = await prisma.programEnrollment.create({
      data: {
        ...data,
        status: 'enrolled'
      },
      include: {
        program: {
          include: {
            academy: true,
            institution: true
          }
        },
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        }
      }
    })

    revalidatePath('/education')
    revalidatePath('/profile')
    return { success: true, enrollment }
  } catch (error) {
    console.error('Error enrolling in program:', error)
    return { success: false, error: 'Failed to enroll in program' }
  }
}

export async function updateEnrollmentProgress(data: {
  enrollmentId: number
  progress: number
  skillsAcquired?: string[]
  finalScore?: number
  status?: string
}) {
  try {
    const enrollment = await prisma.programEnrollment.update({
      where: { id: data.enrollmentId },
      data: {
        progress: data.progress,
        ...(data.skillsAcquired && { skillsAcquired: data.skillsAcquired }),
        ...(data.finalScore && { finalScore: data.finalScore }),
        ...(data.status && { status: data.status }),
        ...(data.status === 'completed' && { completionDate: new Date() })
      },
      include: {
        program: true,
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true
          }
        }
      }
    })

    revalidatePath('/education')
    revalidatePath('/profile')
    return { success: true, enrollment }
  } catch (error) {
    console.error('Error updating enrollment progress:', error)
    return { success: false, error: 'Failed to update progress' }
  }
}

// ========================================
// SKILL ASSESSMENTS
// ========================================

export async function createSkillAssessment(data: {
  enrollmentId: number
  skillType: string
  skillName: string
  initialScore?: number
  currentScore: number
  targetScore?: number
  assessedBy: string
  notes?: string
  mediaUrls: string[]
}) {
  try {
    const assessment = await prisma.skillAssessment.create({
      data,
      include: {
        enrollment: {
          include: {
            program: true,
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true
              }
            }
          }
        }
      }
    })

    revalidatePath('/education')
    return { success: true, assessment }
  } catch (error) {
    console.error('Error creating skill assessment:', error)
    return { success: false, error: 'Failed to create skill assessment' }
  }
}

export async function getSkillAssessments(enrollmentId: number) {
  try {
    const assessments = await prisma.skillAssessment.findMany({
      where: { enrollmentId },
      orderBy: {
        assessmentDate: 'desc'
      }
    })

    return { success: true, assessments }
  } catch (error) {
    console.error('Error fetching skill assessments:', error)
    return { success: false, error: 'Failed to fetch skill assessments' }
  }
}

// ========================================
// CERTIFICATIONS
// ========================================

export async function issueCertification(data: {
  userId: number
  programId: number
  academyId?: number
  institutionId?: number
  title: string
  description: string
  level: string
  certificateUrl: string
  skillsValidated: string[]
  expiresAt?: Date
}) {
  try {
    // Generate unique verification code
    const verificationCode = `CERT-${Date.now()}-${Math.random().toString(36).substr(2, 9).toUpperCase()}`

    const certification = await prisma.certification.create({
      data: {
        ...data,
        verificationCode
      },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        },
        program: true,
        academy: {
          select: {
            id: true,
            name: true,
            logo: true
          }
        },
        institution: {
          select: {
            id: true,
            name: true,
            logo: true
          }
        }
      }
    })

    revalidatePath('/education')
    revalidatePath('/profile')
    return { success: true, certification }
  } catch (error) {
    console.error('Error issuing certification:', error)
    return { success: false, error: 'Failed to issue certification' }
  }
}

export async function verifyCertification(verificationCode: string) {
  try {
    const certification = await prisma.certification.findUnique({
      where: { verificationCode },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true
          }
        },
        program: {
          select: {
            id: true,
            title: true
          }
        },
        academy: {
          select: {
            id: true,
            name: true,
            logo: true
          }
        },
        institution: {
          select: {
            id: true,
            name: true,
            logo: true
          }
        }
      }
    })

    if (!certification || !certification.isActive) {
      return { success: false, error: 'Invalid or inactive certification' }
    }

    if (certification.expiresAt && certification.expiresAt < new Date()) {
      return { success: false, error: 'Certification has expired' }
    }

    return { success: true, certification }
  } catch (error) {
    console.error('Error verifying certification:', error)
    return { success: false, error: 'Failed to verify certification' }
  }
}

export async function getUserCertifications(userId: number) {
  try {
    const certifications = await prisma.certification.findMany({
      where: {
        userId,
        isActive: true
      },
      include: {
        program: {
          select: {
            id: true,
            title: true,
            category: true
          }
        },
        academy: {
          select: {
            id: true,
            name: true,
            logo: true
          }
        },
        institution: {
          select: {
            id: true,
            name: true,
            logo: true
          }
        }
      },
      orderBy: {
        issuedAt: 'desc'
      }
    })

    return { success: true, certifications }
  } catch (error) {
    console.error('Error fetching user certifications:', error)
    return { success: false, error: 'Failed to fetch certifications' }
  }
}
