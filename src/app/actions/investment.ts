'use server'

import { prisma } from '@/lib/prisma'
import { revalidatePath } from 'next/cache'

// ========================================
// INVESTMENT OPPORTUNITIES
// ========================================

export async function createInvestment(data: {
  userId: number
  academyId?: number
  companyId?: number
  title: string
  description: string
  type: string
  category: string
  amount: number
  currency: string
  targetAmount?: number
  riskLevel: string
  expectedReturn?: number
  timeline: string
  businessPlan?: any
  mediaUrls: string[]
  isPublic: boolean
}) {
  try {
    const investment = await prisma.investment.create({
      data: {
        ...data,
        status: 'seeking'
      },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        },
        academy: {
          select: {
            id: true,
            name: true,
            logo: true,
            slug: true
          }
        },
        company: {
          select: {
            id: true,
            name: true,
            logo: true,
            slug: true
          }
        },
        _count: {
          select: {
            applications: true
          }
        }
      }
    })

    revalidatePath('/investments')
    return { success: true, investment }
  } catch (error) {
    console.error('Error creating investment:', error)
    return { success: false, error: 'Failed to create investment opportunity' }
  }
}

export async function getInvestments(filters?: {
  type?: string
  category?: string
  status?: string
  riskLevel?: string
  minAmount?: number
  maxAmount?: number
}) {
  try {
    const investments = await prisma.investment.findMany({
      where: {
        isPublic: true,
        ...(filters?.type && { type: filters.type }),
        ...(filters?.category && { category: filters.category }),
        ...(filters?.status && { status: filters.status }),
        ...(filters?.riskLevel && { riskLevel: filters.riskLevel }),
        ...(filters?.minAmount && { amount: { gte: filters.minAmount } }),
        ...(filters?.maxAmount && { amount: { lte: filters.maxAmount } })
      },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            avatar: true
          }
        },
        academy: {
          select: {
            id: true,
            name: true,
            logo: true,
            slug: true
          }
        },
        company: {
          select: {
            id: true,
            name: true,
            logo: true,
            slug: true
          }
        },
        _count: {
          select: {
            applications: true,
            updates: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    return { success: true, investments }
  } catch (error) {
    console.error('Error fetching investments:', error)
    return { success: false, error: 'Failed to fetch investments' }
  }
}

export async function applyForInvestment(data: {
  investmentId: number
  investorId: number
  amount: number
  currency: string
  terms: any
  proposal: string
}) {
  try {
    const application = await prisma.investmentApplication.create({
      data: {
        ...data,
        status: 'pending'
      },
      include: {
        investment: {
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true
              }
            }
          }
        },
        investor: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        }
      }
    })

    revalidatePath('/investments')
    return { success: true, application }
  } catch (error) {
    console.error('Error applying for investment:', error)
    return { success: false, error: 'Failed to submit investment application' }
  }
}

export async function updateInvestmentApplication(data: {
  applicationId: number
  status: string
  notes?: string
}) {
  try {
    const application = await prisma.investmentApplication.update({
      where: { id: data.applicationId },
      data: {
        status: data.status,
        ...(data.notes && { notes: data.notes }),
        respondedAt: new Date()
      },
      include: {
        investment: true,
        investor: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        }
      }
    })

    // Update investment raised amount if approved
    if (data.status === 'approved') {
      await prisma.investment.update({
        where: { id: application.investmentId },
        data: {
          raisedAmount: {
            increment: application.amount
          }
        }
      })
    }

    revalidatePath('/investments')
    return { success: true, application }
  } catch (error) {
    console.error('Error updating investment application:', error)
    return { success: false, error: 'Failed to update application' }
  }
}

export async function createInvestmentUpdate(data: {
  investmentId: number
  title: string
  content: string
  type: string
  metrics?: any
  mediaUrls: string[]
  isPublic: boolean
}) {
  try {
    const update = await prisma.investmentUpdate.create({
      data,
      include: {
        investment: {
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true
              }
            }
          }
        }
      }
    })

    revalidatePath('/investments')
    return { success: true, update }
  } catch (error) {
    console.error('Error creating investment update:', error)
    return { success: false, error: 'Failed to create investment update' }
  }
}

// ========================================
// BUSINESS PARTNERSHIPS
// ========================================

export async function createBusinessPartnership(data: {
  initiatorId: number
  partnerId: number
  partnerType: string
  partnershipType: string
  title: string
  description: string
  terms: any
  benefits: any
  value?: number
  currency?: string
  startDate?: Date
  endDate?: Date
}) {
  try {
    const partnership = await prisma.businessPartnership.create({
      data: {
        ...data,
        status: 'proposed'
      },
      include: {
        initiator: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        },
        partner: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        }
      }
    })

    revalidatePath('/partnerships')
    return { success: true, partnership }
  } catch (error) {
    console.error('Error creating business partnership:', error)
    return { success: false, error: 'Failed to create partnership proposal' }
  }
}

export async function getBusinessPartnerships(userId: number) {
  try {
    const partnerships = await prisma.businessPartnership.findMany({
      where: {
        OR: [
          { initiatorId: userId },
          { partnerId: userId }
        ]
      },
      include: {
        initiator: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            avatar: true
          }
        },
        partner: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            avatar: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    return { success: true, partnerships }
  } catch (error) {
    console.error('Error fetching business partnerships:', error)
    return { success: false, error: 'Failed to fetch partnerships' }
  }
}

export async function updatePartnershipStatus(data: {
  partnershipId: number
  status: string
  startDate?: Date
  endDate?: Date
}) {
  try {
    const partnership = await prisma.businessPartnership.update({
      where: { id: data.partnershipId },
      data: {
        status: data.status,
        ...(data.startDate && { startDate: data.startDate }),
        ...(data.endDate && { endDate: data.endDate })
      },
      include: {
        initiator: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        },
        partner: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        }
      }
    })

    revalidatePath('/partnerships')
    return { success: true, partnership }
  } catch (error) {
    console.error('Error updating partnership status:', error)
    return { success: false, error: 'Failed to update partnership' }
  }
}

export async function getInvestmentOpportunities(filters?: {
  category?: string
  type?: string
  riskLevel?: string
}) {
  try {
    const opportunities = await prisma.investment.findMany({
      where: {
        isPublic: true,
        status: 'seeking',
        ...(filters?.category && { category: filters.category }),
        ...(filters?.type && { type: filters.type }),
        ...(filters?.riskLevel && { riskLevel: filters.riskLevel })
      },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            avatar: true
          }
        },
        academy: {
          select: {
            id: true,
            name: true,
            logo: true
          }
        },
        company: {
          select: {
            id: true,
            name: true,
            logo: true
          }
        },
        _count: {
          select: {
            applications: true
          }
        }
      },
      orderBy: [
        { createdAt: 'desc' }
      ],
      take: 20
    })

    return { success: true, opportunities }
  } catch (error) {
    console.error('Error fetching investment opportunities:', error)
    return { success: false, error: 'Failed to fetch opportunities' }
  }
}
