'use server'

import { prisma } from '@/lib/prisma'
import { getCurrentUser } from '@/lib/auth'
import { revalidatePath } from 'next/cache'

// Types for academy operations
export interface AcademyRegistrationData {
  academyId: number
  membershipType: string
  position?: string
  skillLevel: string
  talentVideos: string[]
  medicalInfo?: {
    allergies: string
    medications: string
    injuries: string
    doctorContact: string
  }
  emergencyContact: {
    name: string
    phone: string
    relationship: string
  }
  personalInfo?: {
    dateOfBirth: string
    height: string
    weight: string
    dominantFoot: string
    previousExperience: string
    goals: string
  }
}

export interface PerformanceData {
  academyMemberId: number
  trainingId?: number
  metricType: string
  value: number
  unit: string
  details?: any
  recordedBy: string
}

export interface AchievementData {
  academyMemberId: number
  eventId?: number
  type: string
  title: string
  description: string
  level: string
  mediaUrls: string[]
  awardedBy: string
}

export interface TrainingData {
  academyId: number
  title: string
  description: string
  type: string
  skillLevel: string
  scheduledAt: Date
  duration: number
  location: string
  maxParticipants: number
  drills: any
}

// Get all academies
export async function getAcademies() {
  try {
    const academies = await prisma.academy.findMany({
      where: { isActive: true },
      include: {
        _count: {
          select: {
            members: true,
            trainings: true,
            events: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    })

    return { success: true, data: academies }
  } catch (error) {
    console.error('Error fetching academies:', error)
    return { error: 'Failed to fetch academies' }
  }
}

// Get academy by slug
export async function getAcademyBySlug(slug: string) {
  try {
    const academy = await prisma.academy.findUnique({
      where: { slug },
      include: {
        members: {
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                avatar: true
              }
            }
          },
          where: { status: 'active' }
        },
        trainings: {
          where: { 
            scheduledAt: { gte: new Date() },
            status: { in: ['scheduled', 'ongoing'] }
          },
          orderBy: { scheduledAt: 'asc' },
          take: 5
        },
        events: {
          where: { 
            startDate: { gte: new Date() },
            isPublic: true
          },
          orderBy: { startDate: 'asc' },
          take: 5
        },
        partnerships: {
          where: { status: 'active' }
        },
        _count: {
          select: {
            members: true,
            trainings: true,
            events: true
          }
        }
      }
    })

    if (!academy) {
      return { error: 'Academy not found' }
    }

    return { success: true, data: academy }
  } catch (error) {
    console.error('Error fetching academy:', error)
    return { error: 'Failed to fetch academy' }
  }
}

// Register for academy
export async function registerForAcademy(data: AcademyRegistrationData) {
  try {
    const currentUser = await getCurrentUser()
    if (!currentUser) {
      return { error: 'User not authenticated' }
    }

    // Check if user is already a member
    const existingMember = await prisma.academyMember.findUnique({
      where: {
        userId_academyId: {
          userId: currentUser.userId,
          academyId: data.academyId
        }
      }
    })

    if (existingMember) {
      return { error: 'Already a member of this academy' }
    }

    // Create academy membership
    const member = await prisma.academyMember.create({
      data: {
        userId: currentUser.userId,
        academyId: data.academyId,
        membershipType: data.membershipType,
        status: 'active',
        position: data.position,
        skillLevel: data.skillLevel,
        talentVideos: data.talentVideos,
        medicalInfo: data.medicalInfo,
        emergencyContact: data.emergencyContact,
        personalInfo: data.personalInfo
      },
      include: {
        academy: true,
        user: {
          select: {
            firstName: true,
            lastName: true,
            avatar: true
          }
        }
      }
    })

    revalidatePath('/academies')
    revalidatePath(`/academies/${member.academy.slug}`)
    revalidatePath('/profile')

    return { success: true, data: member }
  } catch (error) {
    console.error('Error registering for academy:', error)
    return { error: 'Failed to register for academy' }
  }
}

// Get user's academy memberships
export async function getUserAcademyMemberships() {
  try {
    const currentUser = await getCurrentUser()
    if (!currentUser) {
      return { error: 'User not authenticated' }
    }

    const memberships = await prisma.academyMember.findMany({
      where: { userId: currentUser.userId },
      include: {
        academy: true,
        performances: {
          orderBy: { recordedAt: 'desc' },
          take: 5
        },
        achievements: {
          orderBy: { achievedAt: 'desc' },
          take: 5
        }
      },
      orderBy: { joinedAt: 'desc' }
    })

    return { success: true, data: memberships }
  } catch (error) {
    console.error('Error fetching user academy memberships:', error)
    return { error: 'Failed to fetch academy memberships' }
  }
}

// Record performance
export async function recordPerformance(data: PerformanceData) {
  try {
    const currentUser = await getCurrentUser()
    if (!currentUser) {
      return { error: 'User not authenticated' }
    }

    const performance = await prisma.performance.create({
      data: {
        academyMemberId: data.academyMemberId,
        trainingId: data.trainingId,
        metricType: data.metricType,
        value: data.value,
        unit: data.unit,
        details: data.details,
        recordedBy: data.recordedBy
      },
      include: {
        academyMember: {
          include: {
            user: {
              select: {
                firstName: true,
                lastName: true
              }
            },
            academy: true
          }
        }
      }
    })

    revalidatePath('/academies')
    revalidatePath('/profile')

    return { success: true, data: performance }
  } catch (error) {
    console.error('Error recording performance:', error)
    return { error: 'Failed to record performance' }
  }
}

// Add achievement
export async function addAchievement(data: AchievementData) {
  try {
    const currentUser = await getCurrentUser()
    if (!currentUser) {
      return { error: 'User not authenticated' }
    }

    const achievement = await prisma.achievement.create({
      data: {
        academyMemberId: data.academyMemberId,
        eventId: data.eventId,
        type: data.type,
        title: data.title,
        description: data.description,
        level: data.level,
        mediaUrls: data.mediaUrls,
        awardedBy: data.awardedBy
      },
      include: {
        academyMember: {
          include: {
            user: {
              select: {
                firstName: true,
                lastName: true
              }
            },
            academy: true
          }
        }
      }
    })

    // Create feed item for the achievement
    await createAchievementFeedItem(achievement.id)

    revalidatePath('/academies')
    revalidatePath('/profile')
    revalidatePath('/') // Revalidate home page to show new feed item

    return { success: true, data: achievement }
  } catch (error) {
    console.error('Error adding achievement:', error)
    return { error: 'Failed to add achievement' }
  }
}

// Create training session
export async function createTraining(data: TrainingData) {
  try {
    const currentUser = await getCurrentUser()
    if (!currentUser) {
      return { error: 'User not authenticated' }
    }

    const training = await prisma.training.create({
      data: {
        academyId: data.academyId,
        title: data.title,
        description: data.description,
        type: data.type,
        skillLevel: data.skillLevel,
        scheduledAt: data.scheduledAt,
        duration: data.duration,
        location: data.location,
        maxParticipants: data.maxParticipants,
        status: 'scheduled',
        drills: data.drills
      },
      include: {
        academy: true
      }
    })

    revalidatePath('/academies')
    revalidatePath(`/academies/${training.academy.slug}`)

    return { success: true, data: training }
  } catch (error) {
    console.error('Error creating training:', error)
    return { error: 'Failed to create training' }
  }
}

// Mark training attendance
export async function markTrainingAttendance(trainingId: number, academyMemberId: number, status: string, performance?: any, notes?: string) {
  try {
    const currentUser = await getCurrentUser()
    if (!currentUser) {
      return { error: 'User not authenticated' }
    }

    const attendance = await prisma.trainingAttendance.upsert({
      where: {
        trainingId_academyMemberId: {
          trainingId,
          academyMemberId
        }
      },
      update: {
        status,
        performance,
        notes
      },
      create: {
        trainingId,
        academyMemberId,
        status,
        performance,
        notes
      },
      include: {
        training: true,
        academyMember: {
          include: {
            user: {
              select: {
                firstName: true,
                lastName: true
              }
            }
          }
        }
      }
    })

    revalidatePath('/academies')
    return { success: true, data: attendance }
  } catch (error) {
    console.error('Error marking attendance:', error)
    return { error: 'Failed to mark attendance' }
  }
}

// Create academy event
export async function createAcademyEvent(data: {
  academyId: number
  title: string
  description: string
  type: string
  startDate: Date
  endDate: Date
  location: string
  requirements?: any
  prizes?: any
  isPublic: boolean
}) {
  try {
    const currentUser = await getCurrentUser()
    if (!currentUser) {
      return { error: 'User not authenticated' }
    }

    const event = await prisma.event.create({
      data: {
        academyId: data.academyId,
        title: data.title,
        description: data.description,
        type: data.type,
        startDate: data.startDate,
        endDate: data.endDate,
        location: data.location,
        requirements: data.requirements,
        prizes: data.prizes,
        isPublic: data.isPublic
      },
      include: {
        academy: true
      }
    })

    // Create feed item for the event
    await createEventFeedItem(event.id)

    revalidatePath('/academies')
    revalidatePath(`/academies/${event.academy.slug}`)
    revalidatePath('/') // Revalidate home page to show new feed item

    return { success: true, data: event }
  } catch (error) {
    console.error('Error creating event:', error)
    return { error: 'Failed to create event' }
  }
}

// Register for event
export async function registerForEvent(eventId: number, academyMemberId: number) {
  try {
    const currentUser = await getCurrentUser()
    if (!currentUser) {
      return { error: 'User not authenticated' }
    }

    // Check if already registered
    const existingParticipation = await prisma.eventParticipation.findUnique({
      where: {
        eventId_academyMemberId: {
          eventId,
          academyMemberId
        }
      }
    })

    if (existingParticipation) {
      return { error: 'Already registered for this event' }
    }

    const participation = await prisma.eventParticipation.create({
      data: {
        eventId,
        academyMemberId,
        status: 'registered'
      },
      include: {
        event: true,
        academyMember: {
          include: {
            user: {
              select: {
                firstName: true,
                lastName: true
              }
            }
          }
        }
      }
    })

    revalidatePath('/academies')
    return { success: true, data: participation }
  } catch (error) {
    console.error('Error registering for event:', error)
    return { error: 'Failed to register for event' }
  }
}

// Create academy partnership
export async function createAcademyPartnership(data: {
  academyId: number
  partnerId: number
  partnerType: 'club' | 'company' | 'provider' | 'institution'
  partnershipType: 'scouting' | 'sponsorship' | 'equipment' | 'facility' | 'training' | 'scholarship'
  terms: {
    description: string
    benefits: string[]
    responsibilities: string[]
    financialTerms?: string
    duration?: string
  }
  startDate: Date
  endDate?: Date
}) {
  try {
    const currentUser = await getCurrentUser()
    if (!currentUser) {
      return { error: 'User not authenticated' }
    }

    const partnership = await prisma.academyPartnership.create({
      data: {
        academyId: data.academyId,
        partnerId: data.partnerId,
        partnerType: data.partnerType,
        partnershipType: data.partnershipType,
        terms: data.terms,
        status: 'pending',
        startDate: data.startDate,
        endDate: data.endDate
      }
    })

    revalidatePath('/academies')
    revalidatePath(`/academies/${data.academyId}/partnerships`)
    return { success: true, data: partnership }
  } catch (error) {
    console.error('Error creating partnership:', error)
    return { error: 'Failed to create partnership' }
  }
}

// Update partnership status
export async function updatePartnershipStatus(partnershipId: number, status: 'active' | 'inactive' | 'pending' | 'expired') {
  try {
    const currentUser = await getCurrentUser()
    if (!currentUser) {
      return { error: 'User not authenticated' }
    }

    const partnership = await prisma.academyPartnership.update({
      where: { id: partnershipId },
      data: { status }
    })

    revalidatePath('/academies')
    return { success: true, data: partnership }
  } catch (error) {
    console.error('Error updating partnership status:', error)
    return { error: 'Failed to update partnership status' }
  }
}

// Get academy partnerships
export async function getAcademyPartnerships(academyId: number) {
  try {
    const partnerships = await prisma.academyPartnership.findMany({
      where: { academyId },
      orderBy: { createdAt: 'desc' }
    })

    // Fetch partner details based on partner type
    const partnershipsWithDetails = await Promise.all(
      partnerships.map(async (partnership) => {
        let partnerDetails = null

        switch (partnership.partnerType) {
          case 'club':
            partnerDetails = await prisma.club.findUnique({
              where: { id: partnership.partnerId },
              select: { name: true, logo: true, sport: true }
            })
            break
          case 'company':
            partnerDetails = await prisma.company.findUnique({
              where: { id: partnership.partnerId },
              select: { name: true, logo: true, industry: true }
            })
            break
          case 'provider':
            partnerDetails = await prisma.provider.findUnique({
              where: { id: partnership.partnerId },
              select: { name: true, logo: true }
            })
            break
          case 'institution':
            partnerDetails = await prisma.institution.findUnique({
              where: { id: partnership.partnerId },
              select: { name: true, logo: true, level: true }
            })
            break
        }

        return {
          ...partnership,
          partnerDetails
        }
      })
    )

    return { success: true, data: partnershipsWithDetails }
  } catch (error) {
    console.error('Error fetching academy partnerships:', error)
    return { error: 'Failed to fetch partnerships' }
  }
}

// Get available partners for partnership
export async function getAvailablePartners(partnerType: 'club' | 'company' | 'provider' | 'institution') {
  try {
    let partners = []

    switch (partnerType) {
      case 'club':
        partners = await prisma.club.findMany({
          where: { isActive: true },
          select: { id: true, name: true, logo: true, sport: true },
          orderBy: { name: 'asc' }
        })
        break
      case 'company':
        partners = await prisma.company.findMany({
          where: { isActive: true },
          select: { id: true, name: true, logo: true, industry: true },
          orderBy: { name: 'asc' }
        })
        break
      case 'provider':
        partners = await prisma.provider.findMany({
          where: { isActive: true },
          select: { id: true, name: true, logo: true },
          orderBy: { name: 'asc' }
        })
        break
      case 'institution':
        partners = await prisma.institution.findMany({
          where: { isActive: true },
          select: { id: true, name: true, logo: true, level: true },
          orderBy: { name: 'asc' }
        })
        break
    }

    return { success: true, data: partners }
  } catch (error) {
    console.error('Error fetching available partners:', error)
    return { error: 'Failed to fetch available partners' }
  }
}

// Get academy member performance history
export async function getAcademyMemberPerformance(academyMemberId: number) {
  try {
    const performances = await prisma.performance.findMany({
      where: { academyMemberId },
      include: {
        training: {
          select: {
            title: true,
            type: true,
            scheduledAt: true
          }
        }
      },
      orderBy: { recordedAt: 'desc' }
    })

    return { success: true, data: performances }
  } catch (error) {
    console.error('Error fetching member performance:', error)
    return { error: 'Failed to fetch performance data' }
  }
}

// Get academy member achievements
export async function getAcademyMemberAchievements(academyMemberId: number) {
  try {
    const achievements = await prisma.achievement.findMany({
      where: { academyMemberId },
      include: {
        event: {
          select: {
            title: true,
            type: true,
            startDate: true
          }
        }
      },
      orderBy: { achievedAt: 'desc' }
    })

    return { success: true, data: achievements }
  } catch (error) {
    console.error('Error fetching member achievements:', error)
    return { error: 'Failed to fetch achievements' }
  }
}

// Search academies
export async function searchAcademies(query: string, sport?: string, level?: string, location?: string) {
  try {
    const academies = await prisma.academy.findMany({
      where: {
        isActive: true,
        AND: [
          query ? {
            OR: [
              { name: { contains: query, mode: 'insensitive' } },
              { description: { contains: query, mode: 'insensitive' } }
            ]
          } : {},
          sport ? { sport } : {},
          level ? { level } : {},
          location ? { location: { contains: location, mode: 'insensitive' } } : {}
        ]
      },
      include: {
        _count: {
          select: {
            members: true,
            trainings: true,
            events: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    })

    return { success: true, data: academies }
  } catch (error) {
    console.error('Error searching academies:', error)
    return { error: 'Failed to search academies' }
  }
}

// Get member profile
export async function getMemberProfile(memberId: number) {
  try {
    const currentUser = await getCurrentUser()
    if (!currentUser) {
      return { error: 'User not authenticated' }
    }

    const member = await prisma.academyMember.findUnique({
      where: { id: memberId },
      include: {
        user: {
          select: {
            firstName: true,
            lastName: true,
            avatar: true,
            email: true
          }
        },
        academy: {
          select: {
            name: true,
            sport: true,
            logo: true
          }
        },
        performances: {
          orderBy: { recordedAt: 'desc' },
          take: 20
        },
        achievements: {
          orderBy: { achievedAt: 'desc' },
          take: 20
        }
      }
    })

    if (!member) {
      return { error: 'Member not found' }
    }

    // Check if current user can view this profile
    // (member themselves, academy admin, or coach)
    if (member.userId !== currentUser.userId) {
      // Add additional authorization checks here if needed
      // For now, allow viewing any member profile
    }

    return { success: true, data: member }
  } catch (error) {
    console.error('Error fetching member profile:', error)
    return { error: 'Failed to fetch member profile' }
  }
}

// Get training session
export async function getTrainingSession(trainingId: number) {
  try {
    const currentUser = await getCurrentUser()
    if (!currentUser) {
      return { error: 'User not authenticated' }
    }

    const training = await prisma.training.findUnique({
      where: { id: trainingId },
      include: {
        academy: {
          select: {
            id: true,
            name: true,
            sport: true,
            logo: true,
            slug: true
          }
        },
        attendances: {
          include: {
            academyMember: {
              include: {
                user: {
                  select: {
                    firstName: true,
                    lastName: true,
                    avatar: true
                  }
                }
              }
            }
          }
        }
      }
    })

    if (!training) {
      return { error: 'Training session not found' }
    }

    return { success: true, data: training }
  } catch (error) {
    console.error('Error fetching training session:', error)
    return { error: 'Failed to fetch training session' }
  }
}

// Get academy event
export async function getAcademyEvent(eventId: number) {
  try {
    const currentUser = await getCurrentUser()
    if (!currentUser) {
      return { error: 'User not authenticated' }
    }

    const event = await prisma.event.findUnique({
      where: { id: eventId },
      include: {
        academy: {
          select: {
            id: true,
            name: true,
            sport: true,
            logo: true,
            slug: true
          }
        },
        participants: {
          include: {
            academyMember: {
              include: {
                user: {
                  select: {
                    firstName: true,
                    lastName: true,
                    avatar: true
                  }
                }
              }
            }
          }
        }
      }
    })

    if (!event) {
      return { error: 'Event not found' }
    }

    return { success: true, data: event }
  } catch (error) {
    console.error('Error fetching academy event:', error)
    return { error: 'Failed to fetch academy event' }
  }
}

// Create academy feed item for achievements
export async function createAchievementFeedItem(achievementId: number) {
  try {
    const achievement = await prisma.achievement.findUnique({
      where: { id: achievementId },
      include: {
        academyMember: {
          include: {
            user: {
              select: { firstName: true, lastName: true }
            },
            academy: {
              select: { name: true, sport: true }
            }
          }
        },
        event: {
          select: { title: true, type: true }
        }
      }
    })

    if (!achievement) {
      return { error: 'Achievement not found' }
    }

    const member = achievement.academyMember
    const memberName = `${member.user.firstName} ${member.user.lastName}`

    const feedItem = await prisma.feedItem.create({
      data: {
        type: 'achievement',
        contentId: achievement.id,
        userId: member.userId,
        title: `🏆 ${memberName} achieved ${achievement.title}`,
        description: `${memberName} from ${member.academy.name} has achieved ${achievement.title}${achievement.event ? ` at ${achievement.event.title}` : ''}. ${achievement.description || ''}`,
        mediaUrls: achievement.mediaUrls || [],
        tags: [
          member.academy.sport,
          member.academy.name,
          'achievement',
          achievement.type,
          ...(achievement.event ? [achievement.event.type] : [])
        ].filter(Boolean),
        isActive: true
      }
    })

    return { success: true, data: feedItem }
  } catch (error) {
    console.error('Error creating achievement feed item:', error)
    return { error: 'Failed to create achievement feed item' }
  }
}

// Create academy feed item for events
export async function createEventFeedItem(eventId: number) {
  try {
    const event = await prisma.event.findUnique({
      where: { id: eventId },
      include: {
        academy: {
          select: { name: true, sport: true, logo: true }
        },
        _count: {
          select: { participants: true }
        }
      }
    })

    if (!event) {
      return { error: 'Event not found' }
    }

    const feedItem = await prisma.feedItem.create({
      data: {
        type: 'academy_event',
        contentId: event.id,
        userId: null, // Academy events don't have a specific user
        title: `📅 ${event.title} at ${event.academy.name}`,
        description: `${event.academy.name} is hosting ${event.title}. ${event.description || ''} Location: ${event.location}. ${event._count.participants > 0 ? `${event._count.participants} participants registered.` : 'Registration open!'}`,
        mediaUrls: [], // Events don't have media URLs in the current schema
        tags: [
          event.academy.sport,
          event.academy.name,
          'academy_event',
          event.type,
          event.location
        ].filter(Boolean),
        isActive: true
      }
    })

    return { success: true, data: feedItem }
  } catch (error) {
    console.error('Error creating event feed item:', error)
    return { error: 'Failed to create event feed item' }
  }
}

// Create academy feed item for member highlights
export async function createMemberHighlightFeedItem(memberId: number, highlightData: {
  title: string
  description: string
  mediaUrls: string[]
  category: 'talent' | 'progress' | 'milestone'
}) {
  try {
    const member = await prisma.academyMember.findUnique({
      where: { id: memberId },
      include: {
        user: {
          select: { firstName: true, lastName: true }
        },
        academy: {
          select: { name: true, sport: true }
        }
      }
    })

    if (!member) {
      return { error: 'Member not found' }
    }

    const memberName = `${member.user.firstName} ${member.user.lastName}`

    const feedItem = await prisma.feedItem.create({
      data: {
        type: 'member_highlight',
        contentId: memberId,
        userId: member.userId,
        title: `⭐ ${highlightData.title}`,
        description: `${memberName} from ${member.academy.name}: ${highlightData.description}`,
        mediaUrls: highlightData.mediaUrls,
        tags: [
          member.academy.sport,
          member.academy.name,
          'member_highlight',
          highlightData.category,
          'talent'
        ].filter(Boolean),
        isActive: true
      }
    })

    return { success: true, data: feedItem }
  } catch (error) {
    console.error('Error creating member highlight feed item:', error)
    return { error: 'Failed to create member highlight feed item' }
  }
}

// Academy Media Management Functions

// Upload talent videos for academy member
export async function uploadMemberTalentMedia(memberId: number, mediaFiles: {
  url: string
  type: 'image' | 'video'
  title?: string
  description?: string
  tags?: string[]
}[]) {
  try {
    const currentUser = await getCurrentUser()
    if (!currentUser) {
      return { error: 'Authentication required' }
    }

    const member = await prisma.academyMember.findUnique({
      where: { id: memberId },
      include: { academy: true }
    })

    if (!member) {
      return { error: 'Academy member not found' }
    }

    // Check if user can upload (member themselves or academy admin)
    if (member.userId !== currentUser.userId) {
      // TODO: Add academy admin check when admin system is implemented
      return { error: 'Permission denied' }
    }

    // Get current talent videos
    const currentTalentVideos = Array.isArray(member.talentVideos) ? member.talentVideos as any[] : []

    // Add new media files
    const newTalentVideos = [
      ...currentTalentVideos,
      ...mediaFiles.map(file => ({
        ...file,
        uploadedAt: new Date().toISOString(),
        category: 'talent'
      }))
    ]

    // Update member with new talent videos
    const updatedMember = await prisma.academyMember.update({
      where: { id: memberId },
      data: {
        talentVideos: newTalentVideos
      }
    })

    // Create feed item for talent showcase
    if (mediaFiles.length > 0) {
      await createMemberHighlightFeedItem(memberId, {
        title: `New talent showcase from ${member.academy.name}`,
        description: `${mediaFiles.length} new talent ${mediaFiles.length === 1 ? 'video' : 'videos'} uploaded`,
        mediaUrls: mediaFiles.map(f => f.url),
        category: 'talent'
      })
    }

    return { success: true, member: updatedMember }
  } catch (error) {
    console.error('Error uploading talent media:', error)
    return { error: 'Failed to upload talent media' }
  }
}

// Add media to training session
export async function addTrainingMedia(trainingId: number, mediaFiles: {
  url: string
  type: 'image' | 'video'
  title?: string
  description?: string
  tags?: string[]
}[]) {
  try {
    const currentUser = await getCurrentUser()
    if (!currentUser) {
      return { error: 'Authentication required' }
    }

    const training = await prisma.training.findUnique({
      where: { id: trainingId },
      include: { academy: true }
    })

    if (!training) {
      return { error: 'Training session not found' }
    }

    // TODO: Add academy admin/coach permission check

    // Get current drills data
    const currentDrills = training.drills as any || {}

    // Add media to drills data
    const updatedDrills = {
      ...currentDrills,
      media: [
        ...(currentDrills.media || []),
        ...mediaFiles.map(file => ({
          ...file,
          uploadedAt: new Date().toISOString(),
          category: 'training'
        }))
      ]
    }

    // Update training with new media
    const updatedTraining = await prisma.training.update({
      where: { id: trainingId },
      data: {
        drills: updatedDrills
      }
    })

    return { success: true, training: updatedTraining }
  } catch (error) {
    console.error('Error adding training media:', error)
    return { error: 'Failed to add training media' }
  }
}

// Add media to academy event
export async function addEventMedia(eventId: number, mediaFiles: {
  url: string
  type: 'image' | 'video'
  title?: string
  description?: string
  tags?: string[]
}[]) {
  try {
    const currentUser = await getCurrentUser()
    if (!currentUser) {
      return { error: 'Authentication required' }
    }

    const event = await prisma.event.findUnique({
      where: { id: eventId },
      include: { academy: true }
    })

    if (!event) {
      return { error: 'Event not found' }
    }

    // TODO: Add academy admin permission check

    // For now, we'll store event media in the requirements field as a temporary solution
    // In a production system, you might want to add a dedicated media field to the Event model
    const currentRequirements = event.requirements as any || {}

    const updatedRequirements = {
      ...currentRequirements,
      media: [
        ...(currentRequirements.media || []),
        ...mediaFiles.map(file => ({
          ...file,
          uploadedAt: new Date().toISOString(),
          category: 'event'
        }))
      ]
    }

    const updatedEvent = await prisma.event.update({
      where: { id: eventId },
      data: {
        requirements: updatedRequirements
      }
    })

    return { success: true, event: updatedEvent }
  } catch (error) {
    console.error('Error adding event media:', error)
    return { error: 'Failed to add event media' }
  }
}

// Get academy media by category
export async function getAcademyMedia(academyId: number, category?: 'talent' | 'achievement' | 'training' | 'event') {
  try {
    const academy = await prisma.academy.findUnique({
      where: { id: academyId }
    })

    if (!academy) {
      return { error: 'Academy not found' }
    }

    const media: any[] = []

    if (!category || category === 'talent') {
      // Get talent videos from academy members
      const members = await prisma.academyMember.findMany({
        where: { academyId },
        include: {
          user: { select: { firstName: true, lastName: true, avatar: true } }
        }
      })

      const talentMedia = members.flatMap(member => {
        const talentVideos = Array.isArray(member.talentVideos) ? member.talentVideos as any[] : []
        return talentVideos.map(video => ({
          ...video,
          memberId: member.id,
          memberName: `${member.user.firstName} ${member.user.lastName}`,
          category: 'talent'
        }))
      })

      media.push(...talentMedia)
    }

    if (!category || category === 'achievement') {
      // Get achievement media
      const achievements = await prisma.achievement.findMany({
        where: {
          academyMember: {
            academyId
          }
        },
        include: {
          academyMember: {
            include: {
              user: { select: { firstName: true, lastName: true } }
            }
          }
        }
      })

      const achievementMedia = achievements.flatMap(achievement =>
        achievement.mediaUrls.map(url => ({
          url,
          type: url.includes('.mp4') || url.includes('.webm') ? 'video' : 'image',
          title: achievement.title,
          description: achievement.description,
          category: 'achievement',
          achievementId: achievement.id,
          memberName: `${achievement.academyMember.user.firstName} ${achievement.academyMember.user.lastName}`
        }))
      )

      media.push(...achievementMedia)
    }

    if (!category || category === 'training') {
      // Get training media
      const trainings = await prisma.training.findMany({
        where: { academyId }
      })

      const trainingMedia = trainings.flatMap(training => {
        const drills = training.drills as any || {}
        const trainingMediaFiles = drills.media || []
        return trainingMediaFiles.map((mediaFile: any) => ({
          ...mediaFile,
          trainingId: training.id,
          trainingTitle: training.title,
          category: 'training'
        }))
      })

      media.push(...trainingMedia)
    }

    if (!category || category === 'event') {
      // Get event media
      const events = await prisma.event.findMany({
        where: { academyId }
      })

      const eventMedia = events.flatMap(event => {
        const requirements = event.requirements as any || {}
        const eventMediaFiles = requirements.media || []
        return eventMediaFiles.map((mediaFile: any) => ({
          ...mediaFile,
          eventId: event.id,
          eventTitle: event.title,
          category: 'event'
        }))
      })

      media.push(...eventMedia)
    }

    return { success: true, media }
  } catch (error) {
    console.error('Error getting academy media:', error)
    return { error: 'Failed to get academy media' }
  }
}

// Delete academy media
export async function deleteAcademyMedia(mediaId: string, category: 'talent' | 'achievement' | 'training' | 'event', contextId: number) {
  try {
    const currentUser = await getCurrentUser()
    if (!currentUser) {
      return { error: 'Authentication required' }
    }

    // TODO: Add proper permission checks

    switch (category) {
      case 'talent':
        // Remove from member's talent videos
        const member = await prisma.academyMember.findUnique({
          where: { id: contextId }
        })

        if (!member) {
          return { error: 'Member not found' }
        }

        const talentVideos = Array.isArray(member.talentVideos) ? member.talentVideos as any[] : []
        const updatedTalentVideos = talentVideos.filter((video: any) =>
          video.url !== mediaId && video.publicId !== mediaId
        )

        await prisma.academyMember.update({
          where: { id: contextId },
          data: { talentVideos: updatedTalentVideos }
        })
        break

      case 'training':
        // Remove from training drills media
        const training = await prisma.training.findUnique({
          where: { id: contextId }
        })

        if (!training) {
          return { error: 'Training not found' }
        }

        const drills = training.drills as any || {}
        const updatedMedia = (drills.media || []).filter((media: any) =>
          media.url !== mediaId && media.publicId !== mediaId
        )

        await prisma.training.update({
          where: { id: contextId },
          data: {
            drills: {
              ...drills,
              media: updatedMedia
            }
          }
        })
        break

      case 'event':
        // Remove from event requirements media
        const event = await prisma.event.findUnique({
          where: { id: contextId }
        })

        if (!event) {
          return { error: 'Event not found' }
        }

        const requirements = event.requirements as any || {}
        const updatedEventMedia = (requirements.media || []).filter((media: any) =>
          media.url !== mediaId && media.publicId !== mediaId
        )

        await prisma.event.update({
          where: { id: contextId },
          data: {
            requirements: {
              ...requirements,
              media: updatedEventMedia
            }
          }
        })
        break

      default:
        return { error: 'Invalid category' }
    }

    return { success: true }
  } catch (error) {
    console.error('Error deleting academy media:', error)
    return { error: 'Failed to delete media' }
  }
}

// Create training session feed item
export async function createTrainingSessionFeedItem(trainingId: number) {
  try {
    const training = await prisma.training.findUnique({
      where: { id: trainingId },
      include: {
        academy: {
          select: { name: true, sport: true }
        },
        _count: {
          select: { attendances: true }
        }
      }
    })

    if (!training) {
      return { error: 'Training session not found' }
    }

    const feedItem = await prisma.feedItem.create({
      data: {
        type: 'training_session',
        contentId: training.id,
        userId: null, // Training sessions don't have a specific user
        title: `🏃‍♂️ ${training.title} at ${training.academy.name}`,
        description: `${training.academy.name} completed a ${training.type} training session. ${training.description || ''} ${training._count.attendances > 0 ? `${training._count.attendances} members participated.` : ''}`,
        mediaUrls: [], // Training sessions don't have media URLs in the current schema
        tags: [
          training.academy.sport,
          training.academy.name,
          'training_session',
          training.type,
          training.status
        ].filter(Boolean),
        isActive: true
      }
    })

    revalidatePath('/')
    return { success: true, data: feedItem }
  } catch (error) {
    console.error('Error creating training session feed item:', error)
    return { error: 'Failed to create training session feed item' }
  }
}
