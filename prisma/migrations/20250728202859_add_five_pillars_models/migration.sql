-- CreateTable
CREATE TABLE "EducationalProgram" (
    "id" SERIAL NOT NULL,
    "academyId" INTEGER,
    "institutionId" INTEGER,
    "title" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "category" TEXT NOT NULL,
    "level" TEXT NOT NULL,
    "duration" INTEGER NOT NULL,
    "curriculum" JSONB NOT NULL,
    "prerequisites" TEXT[],
    "learningOutcomes" TEXT[],
    "mediaUrls" TEXT[],
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "EducationalProgram_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ProgramEnrollment" (
    "id" SERIAL NOT NULL,
    "userId" INTEGER NOT NULL,
    "programId" INTEGER NOT NULL,
    "academyMemberId" INTEGER,
    "status" TEXT NOT NULL,
    "progress" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "startDate" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "completionDate" TIMESTAMP(3),
    "finalScore" DOUBLE PRECISION,
    "skillsAcquired" TEXT[],
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ProgramEnrollment_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SkillAssessment" (
    "id" SERIAL NOT NULL,
    "enrollmentId" INTEGER NOT NULL,
    "skillType" TEXT NOT NULL,
    "skillName" TEXT NOT NULL,
    "initialScore" DOUBLE PRECISION,
    "currentScore" DOUBLE PRECISION NOT NULL,
    "targetScore" DOUBLE PRECISION,
    "assessedBy" TEXT NOT NULL,
    "assessmentDate" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "notes" TEXT,
    "mediaUrls" TEXT[],
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "SkillAssessment_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Certification" (
    "id" SERIAL NOT NULL,
    "userId" INTEGER NOT NULL,
    "programId" INTEGER NOT NULL,
    "academyId" INTEGER,
    "institutionId" INTEGER,
    "title" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "level" TEXT NOT NULL,
    "certificateUrl" TEXT NOT NULL,
    "verificationCode" TEXT NOT NULL,
    "skillsValidated" TEXT[],
    "issuedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "expiresAt" TIMESTAMP(3),
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "Certification_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Investment" (
    "id" SERIAL NOT NULL,
    "userId" INTEGER NOT NULL,
    "academyId" INTEGER,
    "companyId" INTEGER,
    "title" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "category" TEXT NOT NULL,
    "amount" DOUBLE PRECISION NOT NULL,
    "currency" TEXT NOT NULL,
    "targetAmount" DOUBLE PRECISION,
    "raisedAmount" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "status" TEXT NOT NULL,
    "riskLevel" TEXT NOT NULL,
    "expectedReturn" DOUBLE PRECISION,
    "timeline" TEXT NOT NULL,
    "businessPlan" JSONB,
    "mediaUrls" TEXT[],
    "isPublic" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Investment_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "InvestmentApplication" (
    "id" SERIAL NOT NULL,
    "investmentId" INTEGER NOT NULL,
    "investorId" INTEGER NOT NULL,
    "amount" DOUBLE PRECISION NOT NULL,
    "currency" TEXT NOT NULL,
    "terms" JSONB NOT NULL,
    "proposal" TEXT NOT NULL,
    "status" TEXT NOT NULL,
    "notes" TEXT,
    "appliedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "respondedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "InvestmentApplication_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "InvestmentUpdate" (
    "id" SERIAL NOT NULL,
    "investmentId" INTEGER NOT NULL,
    "title" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "metrics" JSONB,
    "mediaUrls" TEXT[],
    "isPublic" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "InvestmentUpdate_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BusinessPartnership" (
    "id" SERIAL NOT NULL,
    "initiatorId" INTEGER NOT NULL,
    "partnerId" INTEGER NOT NULL,
    "partnerType" TEXT NOT NULL,
    "partnershipType" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "terms" JSONB NOT NULL,
    "benefits" JSONB NOT NULL,
    "status" TEXT NOT NULL,
    "startDate" TIMESTAMP(3),
    "endDate" TIMESTAMP(3),
    "value" DOUBLE PRECISION,
    "currency" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "BusinessPartnership_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "NutritionProduct" (
    "id" SERIAL NOT NULL,
    "userId" INTEGER NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "category" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "ingredients" JSONB NOT NULL,
    "nutritionFacts" JSONB NOT NULL,
    "targetAudience" TEXT[],
    "certifications" TEXT[],
    "price" DOUBLE PRECISION NOT NULL,
    "currency" TEXT NOT NULL,
    "unit" TEXT NOT NULL,
    "mediaUrls" TEXT[],
    "supplierInfo" JSONB,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "NutritionProduct_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "NutritionOrder" (
    "id" SERIAL NOT NULL,
    "buyerId" INTEGER NOT NULL,
    "sellerId" INTEGER NOT NULL,
    "productId" INTEGER NOT NULL,
    "quantity" INTEGER NOT NULL,
    "unitPrice" DOUBLE PRECISION NOT NULL,
    "totalAmount" DOUBLE PRECISION NOT NULL,
    "currency" TEXT NOT NULL,
    "deliveryAddress" JSONB NOT NULL,
    "status" TEXT NOT NULL,
    "orderDate" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "deliveryDate" TIMESTAMP(3),
    "notes" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "NutritionOrder_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SupplyChainLink" (
    "id" SERIAL NOT NULL,
    "productId" INTEGER NOT NULL,
    "supplierId" INTEGER NOT NULL,
    "linkType" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "location" TEXT NOT NULL,
    "processes" JSONB NOT NULL,
    "quality" JSONB,
    "timeline" JSONB NOT NULL,
    "isVerified" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "SupplyChainLink_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ProductReview" (
    "id" SERIAL NOT NULL,
    "productId" INTEGER NOT NULL,
    "userId" INTEGER NOT NULL,
    "rating" DOUBLE PRECISION NOT NULL,
    "title" TEXT,
    "content" TEXT NOT NULL,
    "verified" BOOLEAN NOT NULL DEFAULT false,
    "helpful" INTEGER NOT NULL DEFAULT 0,
    "mediaUrls" TEXT[],
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ProductReview_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "InternationalNetwork" (
    "id" SERIAL NOT NULL,
    "userId" INTEGER NOT NULL,
    "networkType" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "countries" TEXT[],
    "objectives" TEXT[],
    "activities" JSONB NOT NULL,
    "membershipType" TEXT NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "InternationalNetwork_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "NetworkMembership" (
    "id" SERIAL NOT NULL,
    "networkId" INTEGER NOT NULL,
    "userId" INTEGER NOT NULL,
    "role" TEXT NOT NULL,
    "status" TEXT NOT NULL,
    "joinedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "expertise" TEXT[],
    "interests" TEXT[],
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "NetworkMembership_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "DiplomaticEvent" (
    "id" SERIAL NOT NULL,
    "networkId" INTEGER NOT NULL,
    "organizerId" INTEGER NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "startDate" TIMESTAMP(3) NOT NULL,
    "endDate" TIMESTAMP(3) NOT NULL,
    "location" TEXT NOT NULL,
    "countries" TEXT[],
    "objectives" TEXT[],
    "expectedOutcomes" TEXT[],
    "agenda" JSONB NOT NULL,
    "participants" JSONB,
    "outcomes" JSONB,
    "mediaUrls" TEXT[],
    "isPublic" BOOLEAN NOT NULL DEFAULT true,
    "status" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "DiplomaticEvent_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CulturalExchange" (
    "id" SERIAL NOT NULL,
    "networkId" INTEGER NOT NULL,
    "coordinatorId" INTEGER NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "duration" INTEGER NOT NULL,
    "sourceCountry" TEXT NOT NULL,
    "targetCountry" TEXT NOT NULL,
    "maxParticipants" INTEGER NOT NULL,
    "requirements" JSONB NOT NULL,
    "benefits" TEXT[],
    "curriculum" JSONB,
    "cost" DOUBLE PRECISION,
    "currency" TEXT,
    "applicationDeadline" TIMESTAMP(3),
    "startDate" TIMESTAMP(3) NOT NULL,
    "endDate" TIMESTAMP(3) NOT NULL,
    "status" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "CulturalExchange_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ExchangeApplication" (
    "id" SERIAL NOT NULL,
    "exchangeId" INTEGER NOT NULL,
    "applicantId" INTEGER NOT NULL,
    "motivation" TEXT NOT NULL,
    "experience" JSONB NOT NULL,
    "references" JSONB NOT NULL,
    "documents" TEXT[],
    "status" TEXT NOT NULL,
    "appliedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "reviewedAt" TIMESTAMP(3),
    "feedback" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ExchangeApplication_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TechInnovation" (
    "id" SERIAL NOT NULL,
    "userId" INTEGER NOT NULL,
    "academyId" INTEGER,
    "institutionId" INTEGER,
    "title" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "category" TEXT NOT NULL,
    "stage" TEXT NOT NULL,
    "techStack" TEXT[],
    "features" JSONB NOT NULL,
    "benefits" TEXT[],
    "targetUsers" TEXT[],
    "businessModel" JSONB,
    "mediaUrls" TEXT[],
    "githubUrl" TEXT,
    "demoUrl" TEXT,
    "isOpenSource" BOOLEAN NOT NULL DEFAULT false,
    "isCommercial" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "TechInnovation_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TechAdoption" (
    "id" SERIAL NOT NULL,
    "innovationId" INTEGER NOT NULL,
    "adopterId" INTEGER NOT NULL,
    "adopterType" TEXT NOT NULL,
    "status" TEXT NOT NULL,
    "feedback" TEXT,
    "metrics" JSONB,
    "adoptedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "TechAdoption_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Infrastructure" (
    "id" SERIAL NOT NULL,
    "userId" INTEGER NOT NULL,
    "academyId" INTEGER,
    "title" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "location" TEXT NOT NULL,
    "scope" JSONB NOT NULL,
    "budget" DOUBLE PRECISION NOT NULL,
    "currency" TEXT NOT NULL,
    "fundingStatus" TEXT NOT NULL,
    "timeline" JSONB NOT NULL,
    "status" TEXT NOT NULL,
    "sustainability" JSONB,
    "technology" JSONB,
    "community" JSONB,
    "mediaUrls" TEXT[],
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Infrastructure_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "InfrastructureUpdate" (
    "id" SERIAL NOT NULL,
    "infrastructureId" INTEGER NOT NULL,
    "title" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "progress" DOUBLE PRECISION NOT NULL,
    "budget" JSONB,
    "timeline" JSONB,
    "challenges" TEXT[],
    "mediaUrls" TEXT[],
    "isPublic" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "InfrastructureUpdate_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "EducationalProgram_academyId_idx" ON "EducationalProgram"("academyId");

-- CreateIndex
CREATE INDEX "EducationalProgram_institutionId_idx" ON "EducationalProgram"("institutionId");

-- CreateIndex
CREATE INDEX "EducationalProgram_category_idx" ON "EducationalProgram"("category");

-- CreateIndex
CREATE INDEX "EducationalProgram_level_idx" ON "EducationalProgram"("level");

-- CreateIndex
CREATE INDEX "EducationalProgram_isActive_idx" ON "EducationalProgram"("isActive");

-- CreateIndex
CREATE INDEX "ProgramEnrollment_userId_idx" ON "ProgramEnrollment"("userId");

-- CreateIndex
CREATE INDEX "ProgramEnrollment_programId_idx" ON "ProgramEnrollment"("programId");

-- CreateIndex
CREATE INDEX "ProgramEnrollment_academyMemberId_idx" ON "ProgramEnrollment"("academyMemberId");

-- CreateIndex
CREATE INDEX "ProgramEnrollment_status_idx" ON "ProgramEnrollment"("status");

-- CreateIndex
CREATE UNIQUE INDEX "ProgramEnrollment_userId_programId_key" ON "ProgramEnrollment"("userId", "programId");

-- CreateIndex
CREATE INDEX "SkillAssessment_enrollmentId_idx" ON "SkillAssessment"("enrollmentId");

-- CreateIndex
CREATE INDEX "SkillAssessment_skillType_idx" ON "SkillAssessment"("skillType");

-- CreateIndex
CREATE INDEX "SkillAssessment_skillName_idx" ON "SkillAssessment"("skillName");

-- CreateIndex
CREATE INDEX "SkillAssessment_assessmentDate_idx" ON "SkillAssessment"("assessmentDate");

-- CreateIndex
CREATE UNIQUE INDEX "Certification_verificationCode_key" ON "Certification"("verificationCode");

-- CreateIndex
CREATE INDEX "Certification_userId_idx" ON "Certification"("userId");

-- CreateIndex
CREATE INDEX "Certification_programId_idx" ON "Certification"("programId");

-- CreateIndex
CREATE INDEX "Certification_academyId_idx" ON "Certification"("academyId");

-- CreateIndex
CREATE INDEX "Certification_institutionId_idx" ON "Certification"("institutionId");

-- CreateIndex
CREATE INDEX "Certification_verificationCode_idx" ON "Certification"("verificationCode");

-- CreateIndex
CREATE INDEX "Certification_isActive_idx" ON "Certification"("isActive");

-- CreateIndex
CREATE INDEX "Investment_userId_idx" ON "Investment"("userId");

-- CreateIndex
CREATE INDEX "Investment_academyId_idx" ON "Investment"("academyId");

-- CreateIndex
CREATE INDEX "Investment_companyId_idx" ON "Investment"("companyId");

-- CreateIndex
CREATE INDEX "Investment_type_idx" ON "Investment"("type");

-- CreateIndex
CREATE INDEX "Investment_category_idx" ON "Investment"("category");

-- CreateIndex
CREATE INDEX "Investment_status_idx" ON "Investment"("status");

-- CreateIndex
CREATE INDEX "Investment_isPublic_idx" ON "Investment"("isPublic");

-- CreateIndex
CREATE INDEX "InvestmentApplication_investmentId_idx" ON "InvestmentApplication"("investmentId");

-- CreateIndex
CREATE INDEX "InvestmentApplication_investorId_idx" ON "InvestmentApplication"("investorId");

-- CreateIndex
CREATE INDEX "InvestmentApplication_status_idx" ON "InvestmentApplication"("status");

-- CreateIndex
CREATE UNIQUE INDEX "InvestmentApplication_investmentId_investorId_key" ON "InvestmentApplication"("investmentId", "investorId");

-- CreateIndex
CREATE INDEX "InvestmentUpdate_investmentId_idx" ON "InvestmentUpdate"("investmentId");

-- CreateIndex
CREATE INDEX "InvestmentUpdate_type_idx" ON "InvestmentUpdate"("type");

-- CreateIndex
CREATE INDEX "InvestmentUpdate_createdAt_idx" ON "InvestmentUpdate"("createdAt");

-- CreateIndex
CREATE INDEX "BusinessPartnership_initiatorId_idx" ON "BusinessPartnership"("initiatorId");

-- CreateIndex
CREATE INDEX "BusinessPartnership_partnerId_idx" ON "BusinessPartnership"("partnerId");

-- CreateIndex
CREATE INDEX "BusinessPartnership_partnerType_idx" ON "BusinessPartnership"("partnerType");

-- CreateIndex
CREATE INDEX "BusinessPartnership_partnershipType_idx" ON "BusinessPartnership"("partnershipType");

-- CreateIndex
CREATE INDEX "BusinessPartnership_status_idx" ON "BusinessPartnership"("status");

-- CreateIndex
CREATE INDEX "NutritionProduct_userId_idx" ON "NutritionProduct"("userId");

-- CreateIndex
CREATE INDEX "NutritionProduct_category_idx" ON "NutritionProduct"("category");

-- CreateIndex
CREATE INDEX "NutritionProduct_type_idx" ON "NutritionProduct"("type");

-- CreateIndex
CREATE INDEX "NutritionProduct_targetAudience_idx" ON "NutritionProduct"("targetAudience");

-- CreateIndex
CREATE INDEX "NutritionProduct_isActive_idx" ON "NutritionProduct"("isActive");

-- CreateIndex
CREATE INDEX "NutritionOrder_buyerId_idx" ON "NutritionOrder"("buyerId");

-- CreateIndex
CREATE INDEX "NutritionOrder_sellerId_idx" ON "NutritionOrder"("sellerId");

-- CreateIndex
CREATE INDEX "NutritionOrder_productId_idx" ON "NutritionOrder"("productId");

-- CreateIndex
CREATE INDEX "NutritionOrder_status_idx" ON "NutritionOrder"("status");

-- CreateIndex
CREATE INDEX "SupplyChainLink_productId_idx" ON "SupplyChainLink"("productId");

-- CreateIndex
CREATE INDEX "SupplyChainLink_supplierId_idx" ON "SupplyChainLink"("supplierId");

-- CreateIndex
CREATE INDEX "SupplyChainLink_linkType_idx" ON "SupplyChainLink"("linkType");

-- CreateIndex
CREATE INDEX "SupplyChainLink_location_idx" ON "SupplyChainLink"("location");

-- CreateIndex
CREATE INDEX "ProductReview_productId_idx" ON "ProductReview"("productId");

-- CreateIndex
CREATE INDEX "ProductReview_userId_idx" ON "ProductReview"("userId");

-- CreateIndex
CREATE INDEX "ProductReview_rating_idx" ON "ProductReview"("rating");

-- CreateIndex
CREATE INDEX "ProductReview_verified_idx" ON "ProductReview"("verified");

-- CreateIndex
CREATE UNIQUE INDEX "ProductReview_productId_userId_key" ON "ProductReview"("productId", "userId");

-- CreateIndex
CREATE INDEX "InternationalNetwork_userId_idx" ON "InternationalNetwork"("userId");

-- CreateIndex
CREATE INDEX "InternationalNetwork_networkType_idx" ON "InternationalNetwork"("networkType");

-- CreateIndex
CREATE INDEX "InternationalNetwork_countries_idx" ON "InternationalNetwork"("countries");

-- CreateIndex
CREATE INDEX "InternationalNetwork_isActive_idx" ON "InternationalNetwork"("isActive");

-- CreateIndex
CREATE INDEX "NetworkMembership_networkId_idx" ON "NetworkMembership"("networkId");

-- CreateIndex
CREATE INDEX "NetworkMembership_userId_idx" ON "NetworkMembership"("userId");

-- CreateIndex
CREATE INDEX "NetworkMembership_role_idx" ON "NetworkMembership"("role");

-- CreateIndex
CREATE INDEX "NetworkMembership_status_idx" ON "NetworkMembership"("status");

-- CreateIndex
CREATE UNIQUE INDEX "NetworkMembership_networkId_userId_key" ON "NetworkMembership"("networkId", "userId");

-- CreateIndex
CREATE INDEX "DiplomaticEvent_networkId_idx" ON "DiplomaticEvent"("networkId");

-- CreateIndex
CREATE INDEX "DiplomaticEvent_organizerId_idx" ON "DiplomaticEvent"("organizerId");

-- CreateIndex
CREATE INDEX "DiplomaticEvent_type_idx" ON "DiplomaticEvent"("type");

-- CreateIndex
CREATE INDEX "DiplomaticEvent_startDate_idx" ON "DiplomaticEvent"("startDate");

-- CreateIndex
CREATE INDEX "DiplomaticEvent_countries_idx" ON "DiplomaticEvent"("countries");

-- CreateIndex
CREATE INDEX "DiplomaticEvent_status_idx" ON "DiplomaticEvent"("status");

-- CreateIndex
CREATE INDEX "CulturalExchange_networkId_idx" ON "CulturalExchange"("networkId");

-- CreateIndex
CREATE INDEX "CulturalExchange_coordinatorId_idx" ON "CulturalExchange"("coordinatorId");

-- CreateIndex
CREATE INDEX "CulturalExchange_type_idx" ON "CulturalExchange"("type");

-- CreateIndex
CREATE INDEX "CulturalExchange_sourceCountry_idx" ON "CulturalExchange"("sourceCountry");

-- CreateIndex
CREATE INDEX "CulturalExchange_targetCountry_idx" ON "CulturalExchange"("targetCountry");

-- CreateIndex
CREATE INDEX "CulturalExchange_status_idx" ON "CulturalExchange"("status");

-- CreateIndex
CREATE INDEX "ExchangeApplication_exchangeId_idx" ON "ExchangeApplication"("exchangeId");

-- CreateIndex
CREATE INDEX "ExchangeApplication_applicantId_idx" ON "ExchangeApplication"("applicantId");

-- CreateIndex
CREATE INDEX "ExchangeApplication_status_idx" ON "ExchangeApplication"("status");

-- CreateIndex
CREATE UNIQUE INDEX "ExchangeApplication_exchangeId_applicantId_key" ON "ExchangeApplication"("exchangeId", "applicantId");

-- CreateIndex
CREATE INDEX "TechInnovation_userId_idx" ON "TechInnovation"("userId");

-- CreateIndex
CREATE INDEX "TechInnovation_academyId_idx" ON "TechInnovation"("academyId");

-- CreateIndex
CREATE INDEX "TechInnovation_institutionId_idx" ON "TechInnovation"("institutionId");

-- CreateIndex
CREATE INDEX "TechInnovation_category_idx" ON "TechInnovation"("category");

-- CreateIndex
CREATE INDEX "TechInnovation_stage_idx" ON "TechInnovation"("stage");

-- CreateIndex
CREATE INDEX "TechInnovation_isOpenSource_idx" ON "TechInnovation"("isOpenSource");

-- CreateIndex
CREATE INDEX "TechAdoption_innovationId_idx" ON "TechAdoption"("innovationId");

-- CreateIndex
CREATE INDEX "TechAdoption_adopterId_idx" ON "TechAdoption"("adopterId");

-- CreateIndex
CREATE INDEX "TechAdoption_adopterType_idx" ON "TechAdoption"("adopterType");

-- CreateIndex
CREATE INDEX "TechAdoption_status_idx" ON "TechAdoption"("status");

-- CreateIndex
CREATE UNIQUE INDEX "TechAdoption_innovationId_adopterId_key" ON "TechAdoption"("innovationId", "adopterId");

-- CreateIndex
CREATE INDEX "Infrastructure_userId_idx" ON "Infrastructure"("userId");

-- CreateIndex
CREATE INDEX "Infrastructure_academyId_idx" ON "Infrastructure"("academyId");

-- CreateIndex
CREATE INDEX "Infrastructure_type_idx" ON "Infrastructure"("type");

-- CreateIndex
CREATE INDEX "Infrastructure_location_idx" ON "Infrastructure"("location");

-- CreateIndex
CREATE INDEX "Infrastructure_status_idx" ON "Infrastructure"("status");

-- CreateIndex
CREATE INDEX "Infrastructure_fundingStatus_idx" ON "Infrastructure"("fundingStatus");

-- CreateIndex
CREATE INDEX "InfrastructureUpdate_infrastructureId_idx" ON "InfrastructureUpdate"("infrastructureId");

-- CreateIndex
CREATE INDEX "InfrastructureUpdate_type_idx" ON "InfrastructureUpdate"("type");

-- CreateIndex
CREATE INDEX "InfrastructureUpdate_createdAt_idx" ON "InfrastructureUpdate"("createdAt");

-- AddForeignKey
ALTER TABLE "EducationalProgram" ADD CONSTRAINT "EducationalProgram_academyId_fkey" FOREIGN KEY ("academyId") REFERENCES "Academy"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EducationalProgram" ADD CONSTRAINT "EducationalProgram_institutionId_fkey" FOREIGN KEY ("institutionId") REFERENCES "Institution"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProgramEnrollment" ADD CONSTRAINT "ProgramEnrollment_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProgramEnrollment" ADD CONSTRAINT "ProgramEnrollment_programId_fkey" FOREIGN KEY ("programId") REFERENCES "EducationalProgram"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProgramEnrollment" ADD CONSTRAINT "ProgramEnrollment_academyMemberId_fkey" FOREIGN KEY ("academyMemberId") REFERENCES "AcademyMember"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SkillAssessment" ADD CONSTRAINT "SkillAssessment_enrollmentId_fkey" FOREIGN KEY ("enrollmentId") REFERENCES "ProgramEnrollment"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Certification" ADD CONSTRAINT "Certification_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Certification" ADD CONSTRAINT "Certification_programId_fkey" FOREIGN KEY ("programId") REFERENCES "EducationalProgram"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Certification" ADD CONSTRAINT "Certification_academyId_fkey" FOREIGN KEY ("academyId") REFERENCES "Academy"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Certification" ADD CONSTRAINT "Certification_institutionId_fkey" FOREIGN KEY ("institutionId") REFERENCES "Institution"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Investment" ADD CONSTRAINT "Investment_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Investment" ADD CONSTRAINT "Investment_academyId_fkey" FOREIGN KEY ("academyId") REFERENCES "Academy"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Investment" ADD CONSTRAINT "Investment_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "Company"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "InvestmentApplication" ADD CONSTRAINT "InvestmentApplication_investmentId_fkey" FOREIGN KEY ("investmentId") REFERENCES "Investment"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "InvestmentApplication" ADD CONSTRAINT "InvestmentApplication_investorId_fkey" FOREIGN KEY ("investorId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "InvestmentUpdate" ADD CONSTRAINT "InvestmentUpdate_investmentId_fkey" FOREIGN KEY ("investmentId") REFERENCES "Investment"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BusinessPartnership" ADD CONSTRAINT "BusinessPartnership_initiatorId_fkey" FOREIGN KEY ("initiatorId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BusinessPartnership" ADD CONSTRAINT "BusinessPartnership_partnerId_fkey" FOREIGN KEY ("partnerId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "NutritionProduct" ADD CONSTRAINT "NutritionProduct_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "NutritionOrder" ADD CONSTRAINT "NutritionOrder_buyerId_fkey" FOREIGN KEY ("buyerId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "NutritionOrder" ADD CONSTRAINT "NutritionOrder_sellerId_fkey" FOREIGN KEY ("sellerId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "NutritionOrder" ADD CONSTRAINT "NutritionOrder_productId_fkey" FOREIGN KEY ("productId") REFERENCES "NutritionProduct"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SupplyChainLink" ADD CONSTRAINT "SupplyChainLink_productId_fkey" FOREIGN KEY ("productId") REFERENCES "NutritionProduct"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SupplyChainLink" ADD CONSTRAINT "SupplyChainLink_supplierId_fkey" FOREIGN KEY ("supplierId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProductReview" ADD CONSTRAINT "ProductReview_productId_fkey" FOREIGN KEY ("productId") REFERENCES "NutritionProduct"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProductReview" ADD CONSTRAINT "ProductReview_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "InternationalNetwork" ADD CONSTRAINT "InternationalNetwork_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "NetworkMembership" ADD CONSTRAINT "NetworkMembership_networkId_fkey" FOREIGN KEY ("networkId") REFERENCES "InternationalNetwork"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "NetworkMembership" ADD CONSTRAINT "NetworkMembership_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "DiplomaticEvent" ADD CONSTRAINT "DiplomaticEvent_networkId_fkey" FOREIGN KEY ("networkId") REFERENCES "InternationalNetwork"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "DiplomaticEvent" ADD CONSTRAINT "DiplomaticEvent_organizerId_fkey" FOREIGN KEY ("organizerId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CulturalExchange" ADD CONSTRAINT "CulturalExchange_networkId_fkey" FOREIGN KEY ("networkId") REFERENCES "InternationalNetwork"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CulturalExchange" ADD CONSTRAINT "CulturalExchange_coordinatorId_fkey" FOREIGN KEY ("coordinatorId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ExchangeApplication" ADD CONSTRAINT "ExchangeApplication_exchangeId_fkey" FOREIGN KEY ("exchangeId") REFERENCES "CulturalExchange"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ExchangeApplication" ADD CONSTRAINT "ExchangeApplication_applicantId_fkey" FOREIGN KEY ("applicantId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TechInnovation" ADD CONSTRAINT "TechInnovation_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TechInnovation" ADD CONSTRAINT "TechInnovation_academyId_fkey" FOREIGN KEY ("academyId") REFERENCES "Academy"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TechInnovation" ADD CONSTRAINT "TechInnovation_institutionId_fkey" FOREIGN KEY ("institutionId") REFERENCES "Institution"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TechAdoption" ADD CONSTRAINT "TechAdoption_innovationId_fkey" FOREIGN KEY ("innovationId") REFERENCES "TechInnovation"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TechAdoption" ADD CONSTRAINT "TechAdoption_adopterId_fkey" FOREIGN KEY ("adopterId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Infrastructure" ADD CONSTRAINT "Infrastructure_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Infrastructure" ADD CONSTRAINT "Infrastructure_academyId_fkey" FOREIGN KEY ("academyId") REFERENCES "Academy"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "InfrastructureUpdate" ADD CONSTRAINT "InfrastructureUpdate_infrastructureId_fkey" FOREIGN KEY ("infrastructureId") REFERENCES "Infrastructure"("id") ON DELETE CASCADE ON UPDATE CASCADE;
