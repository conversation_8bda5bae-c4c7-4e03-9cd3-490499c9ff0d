-- CreateTable
CREATE TABLE "Academy" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "slug" TEXT NOT NULL,
    "sport" TEXT NOT NULL,
    "level" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "logo" TEXT,
    "content" JSONB NOT NULL,
    "location" TEXT NOT NULL,
    "contactEmail" TEXT NOT NULL,
    "contactPhone" TEXT NOT NULL,
    "facilities" JSONB NOT NULL,
    "programs" JSONB NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Academy_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "AcademyMember" (
    "id" SERIAL NOT NULL,
    "userId" INTEGER NOT NULL,
    "academyId" INTEGER NOT NULL,
    "membershipType" TEXT NOT NULL,
    "status" TEXT NOT NULL,
    "position" TEXT,
    "skillLevel" TEXT NOT NULL,
    "talentVideos" JSONB NOT NULL,
    "medicalInfo" JSONB,
    "emergencyContact" JSONB NOT NULL,
    "joinedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "expiresAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "AcademyMember_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Performance" (
    "id" SERIAL NOT NULL,
    "academyMemberId" INTEGER NOT NULL,
    "trainingId" INTEGER,
    "metricType" TEXT NOT NULL,
    "value" DOUBLE PRECISION NOT NULL,
    "unit" TEXT NOT NULL,
    "details" JSONB,
    "recordedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "recordedBy" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "Performance_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Achievement" (
    "id" SERIAL NOT NULL,
    "academyMemberId" INTEGER NOT NULL,
    "eventId" INTEGER,
    "type" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "level" TEXT NOT NULL,
    "mediaUrls" TEXT[],
    "achievedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "awardedBy" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "Achievement_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Training" (
    "id" SERIAL NOT NULL,
    "academyId" INTEGER NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "skillLevel" TEXT NOT NULL,
    "scheduledAt" TIMESTAMP(3) NOT NULL,
    "duration" INTEGER NOT NULL,
    "location" TEXT NOT NULL,
    "maxParticipants" INTEGER NOT NULL,
    "status" TEXT NOT NULL,
    "drills" JSONB NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Training_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TrainingAttendance" (
    "id" SERIAL NOT NULL,
    "trainingId" INTEGER NOT NULL,
    "academyMemberId" INTEGER NOT NULL,
    "status" TEXT NOT NULL,
    "performance" JSONB,
    "notes" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "TrainingAttendance_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Event" (
    "id" SERIAL NOT NULL,
    "academyId" INTEGER NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "startDate" TIMESTAMP(3) NOT NULL,
    "endDate" TIMESTAMP(3) NOT NULL,
    "location" TEXT NOT NULL,
    "requirements" JSONB,
    "prizes" JSONB,
    "isPublic" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Event_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "EventParticipation" (
    "id" SERIAL NOT NULL,
    "eventId" INTEGER NOT NULL,
    "academyMemberId" INTEGER NOT NULL,
    "status" TEXT NOT NULL,
    "results" JSONB,
    "position" INTEGER,
    "mediaUrls" TEXT[],
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "EventParticipation_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "AcademyPartnership" (
    "id" SERIAL NOT NULL,
    "academyId" INTEGER NOT NULL,
    "partnerId" INTEGER NOT NULL,
    "partnerType" TEXT NOT NULL,
    "partnershipType" TEXT NOT NULL,
    "terms" JSONB NOT NULL,
    "status" TEXT NOT NULL,
    "startDate" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "endDate" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "AcademyPartnership_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "Academy_slug_key" ON "Academy"("slug");

-- CreateIndex
CREATE INDEX "Academy_slug_idx" ON "Academy"("slug");

-- CreateIndex
CREATE INDEX "Academy_sport_idx" ON "Academy"("sport");

-- CreateIndex
CREATE INDEX "Academy_level_idx" ON "Academy"("level");

-- CreateIndex
CREATE INDEX "Academy_location_idx" ON "Academy"("location");

-- CreateIndex
CREATE INDEX "Academy_isActive_idx" ON "Academy"("isActive");

-- CreateIndex
CREATE INDEX "AcademyMember_userId_idx" ON "AcademyMember"("userId");

-- CreateIndex
CREATE INDEX "AcademyMember_academyId_idx" ON "AcademyMember"("academyId");

-- CreateIndex
CREATE INDEX "AcademyMember_status_idx" ON "AcademyMember"("status");

-- CreateIndex
CREATE INDEX "AcademyMember_skillLevel_idx" ON "AcademyMember"("skillLevel");

-- CreateIndex
CREATE INDEX "AcademyMember_position_idx" ON "AcademyMember"("position");

-- CreateIndex
CREATE UNIQUE INDEX "AcademyMember_userId_academyId_key" ON "AcademyMember"("userId", "academyId");

-- CreateIndex
CREATE INDEX "Performance_academyMemberId_idx" ON "Performance"("academyMemberId");

-- CreateIndex
CREATE INDEX "Performance_trainingId_idx" ON "Performance"("trainingId");

-- CreateIndex
CREATE INDEX "Performance_metricType_idx" ON "Performance"("metricType");

-- CreateIndex
CREATE INDEX "Performance_recordedAt_idx" ON "Performance"("recordedAt");

-- CreateIndex
CREATE INDEX "Achievement_academyMemberId_idx" ON "Achievement"("academyMemberId");

-- CreateIndex
CREATE INDEX "Achievement_eventId_idx" ON "Achievement"("eventId");

-- CreateIndex
CREATE INDEX "Achievement_type_idx" ON "Achievement"("type");

-- CreateIndex
CREATE INDEX "Achievement_level_idx" ON "Achievement"("level");

-- CreateIndex
CREATE INDEX "Achievement_achievedAt_idx" ON "Achievement"("achievedAt");

-- CreateIndex
CREATE INDEX "Training_academyId_idx" ON "Training"("academyId");

-- CreateIndex
CREATE INDEX "Training_scheduledAt_idx" ON "Training"("scheduledAt");

-- CreateIndex
CREATE INDEX "Training_type_idx" ON "Training"("type");

-- CreateIndex
CREATE INDEX "Training_skillLevel_idx" ON "Training"("skillLevel");

-- CreateIndex
CREATE INDEX "Training_status_idx" ON "Training"("status");

-- CreateIndex
CREATE INDEX "TrainingAttendance_trainingId_idx" ON "TrainingAttendance"("trainingId");

-- CreateIndex
CREATE INDEX "TrainingAttendance_academyMemberId_idx" ON "TrainingAttendance"("academyMemberId");

-- CreateIndex
CREATE INDEX "TrainingAttendance_status_idx" ON "TrainingAttendance"("status");

-- CreateIndex
CREATE UNIQUE INDEX "TrainingAttendance_trainingId_academyMemberId_key" ON "TrainingAttendance"("trainingId", "academyMemberId");

-- CreateIndex
CREATE INDEX "Event_academyId_idx" ON "Event"("academyId");

-- CreateIndex
CREATE INDEX "Event_startDate_idx" ON "Event"("startDate");

-- CreateIndex
CREATE INDEX "Event_type_idx" ON "Event"("type");

-- CreateIndex
CREATE INDEX "Event_isPublic_idx" ON "Event"("isPublic");

-- CreateIndex
CREATE INDEX "EventParticipation_eventId_idx" ON "EventParticipation"("eventId");

-- CreateIndex
CREATE INDEX "EventParticipation_academyMemberId_idx" ON "EventParticipation"("academyMemberId");

-- CreateIndex
CREATE INDEX "EventParticipation_status_idx" ON "EventParticipation"("status");

-- CreateIndex
CREATE INDEX "EventParticipation_position_idx" ON "EventParticipation"("position");

-- CreateIndex
CREATE UNIQUE INDEX "EventParticipation_eventId_academyMemberId_key" ON "EventParticipation"("eventId", "academyMemberId");

-- CreateIndex
CREATE INDEX "AcademyPartnership_academyId_idx" ON "AcademyPartnership"("academyId");

-- CreateIndex
CREATE INDEX "AcademyPartnership_partnerId_idx" ON "AcademyPartnership"("partnerId");

-- CreateIndex
CREATE INDEX "AcademyPartnership_partnerType_idx" ON "AcademyPartnership"("partnerType");

-- CreateIndex
CREATE INDEX "AcademyPartnership_partnershipType_idx" ON "AcademyPartnership"("partnershipType");

-- CreateIndex
CREATE INDEX "AcademyPartnership_status_idx" ON "AcademyPartnership"("status");

-- AddForeignKey
ALTER TABLE "AcademyMember" ADD CONSTRAINT "AcademyMember_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AcademyMember" ADD CONSTRAINT "AcademyMember_academyId_fkey" FOREIGN KEY ("academyId") REFERENCES "Academy"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Performance" ADD CONSTRAINT "Performance_academyMemberId_fkey" FOREIGN KEY ("academyMemberId") REFERENCES "AcademyMember"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Performance" ADD CONSTRAINT "Performance_trainingId_fkey" FOREIGN KEY ("trainingId") REFERENCES "Training"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Achievement" ADD CONSTRAINT "Achievement_academyMemberId_fkey" FOREIGN KEY ("academyMemberId") REFERENCES "AcademyMember"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Achievement" ADD CONSTRAINT "Achievement_eventId_fkey" FOREIGN KEY ("eventId") REFERENCES "Event"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Training" ADD CONSTRAINT "Training_academyId_fkey" FOREIGN KEY ("academyId") REFERENCES "Academy"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TrainingAttendance" ADD CONSTRAINT "TrainingAttendance_trainingId_fkey" FOREIGN KEY ("trainingId") REFERENCES "Training"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TrainingAttendance" ADD CONSTRAINT "TrainingAttendance_academyMemberId_fkey" FOREIGN KEY ("academyMemberId") REFERENCES "AcademyMember"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Event" ADD CONSTRAINT "Event_academyId_fkey" FOREIGN KEY ("academyId") REFERENCES "Academy"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EventParticipation" ADD CONSTRAINT "EventParticipation_eventId_fkey" FOREIGN KEY ("eventId") REFERENCES "Event"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EventParticipation" ADD CONSTRAINT "EventParticipation_academyMemberId_fkey" FOREIGN KEY ("academyMemberId") REFERENCES "AcademyMember"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AcademyPartnership" ADD CONSTRAINT "AcademyPartnership_academyId_fkey" FOREIGN KEY ("academyId") REFERENCES "Academy"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
