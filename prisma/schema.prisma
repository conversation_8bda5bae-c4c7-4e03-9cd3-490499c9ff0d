// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User model - Core user management
model User {
  id            Int       @id @default(autoincrement())
  email         String    @unique
  phone         String    @unique // Made required for connections
  password      String
  firstName     String
  lastName      String
  country       String    // ISO country code (e.g., "TZ")
  profession    String?   // e.g., "software engineer"
  businessType  String?   // e.g., "retail", "services", "manufacturing", "agriculture"
  skills        String[]  // e.g., ["JavaScript", "React"]
  clubIds       Int[]     // IDs of favorite clubs (e.g., Simba)
  providerId    Int?      // Linked telecom provider
  institutionId Int?      // Linked institution
  avatar        String?   // Profile image URL
  isVerified    Boolean   @default(false)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // Relations
  cv              CV?
  portfolios      Portfolio[]
  products        Product[]
  orders          Order[]     @relation("UserOrders")
  sales           Order[]     @relation("UserSales")
  interactions    Interaction[]
  payments        Payment[]
  memberships     Membership[]
  innovations     Innovation[]
  feedItems       FeedItem[]
  academyMembers  AcademyMember[]
  provider        Provider?   @relation(fields: [providerId], references: [id])
  institution     Institution? @relation(fields: [institutionId], references: [id])

  // Education & Skills Relations
  programEnrollments    ProgramEnrollment[]
  certifications        Certification[]

  // Trade & Investment Relations
  investments           Investment[]
  investmentApplications InvestmentApplication[]
  initiatedPartnerships BusinessPartnership[] @relation("InitiatedPartnerships")
  receivedPartnerships  BusinessPartnership[] @relation("ReceivedPartnerships")

  // Agro-processing & Nutrition Relations
  nutritionProducts     NutritionProduct[]
  nutritionBuys         NutritionOrder[] @relation("NutritionBuyer")
  nutritionSales        NutritionOrder[] @relation("NutritionSeller")
  supplyChainLinks      SupplyChainLink[]
  productReviews        ProductReview[]

  // Networking & Diplomatic Relations
  internationalNetworks InternationalNetwork[]
  networkMemberships    NetworkMembership[]
  diplomaticEvents      DiplomaticEvent[]
  culturalExchanges     CulturalExchange[]
  exchangeApplications  ExchangeApplication[]

  // Technology & Infrastructure Relations
  techInnovations       TechInnovation[]
  techAdoptions         TechAdoption[]
  infrastructures       Infrastructure[]

  @@index([email])
  @@index([phone])
  @@index([country])
  @@index([profession])
  @@index([businessType])
  @@index([skills])
  @@index([providerId])
  @@index([institutionId])
  @@index([clubIds])
}

// Provider model - Telecommunication providers
model Provider {
  id        Int      @id @default(autoincrement())
  name      String   // e.g., "Vodacom"
  slug      String   @unique
  logo      String?  // Logo URL
  content   Json     // Page content
  services  Json[]   // Services (name, price, API link)
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  users      User[]

  @@index([slug])
  @@index([isActive])
}

// Club model - Sports clubs
model Club {
  id          Int      @id @default(autoincrement())
  name        String   // e.g., "Simba"
  slug        String   @unique
  sport       String   // e.g., "football"
  logo        String?  // Club logo URL
  content     Json     // Page content
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  memberships Membership[]

  @@index([slug])
  @@index([sport])
  @@index([isActive])
}

// Institution model - Educational institutions
model Institution {
  id          Int      @id @default(autoincrement())
  name        String   // e.g., "Kairuki Institute"
  slug        String   @unique
  level       String   // e.g., "university"
  logo        String?  // Institution logo URL
  content     Json     // Page content
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  users       User[]
  innovations Innovation[]

  // New Relations for Five Pillars
  educationalPrograms EducationalProgram[]
  certifications      Certification[]
  techInnovations     TechInnovation[]

  @@index([slug])
  @@index([level])
  @@index([isActive])
}

// Innovation model - Student innovations
model Innovation {
  id            Int         @id @default(autoincrement())
  userId        Int
  institutionId Int
  title         String
  description   String
  mediaUrls     String[]    // Cloudinary URLs
  tags          String[]    // Innovation tags
  isPublished   Boolean     @default(false)
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt

  // Relations
  user        User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  institution Institution @relation(fields: [institutionId], references: [id])

  @@index([userId])
  @@index([institutionId])
  @@index([isPublished])
  @@index([tags])
}

// Membership model - Club memberships
model Membership {
  id        Int      @id @default(autoincrement())
  userId    Int
  clubId    Int
  status    String   // e.g., "active", "inactive", "pending"
  joinedAt  DateTime @default(now())
  expiresAt DateTime?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  club Club @relation(fields: [clubId], references: [id])

  @@unique([userId, clubId])
  @@index([userId])
  @@index([clubId])
  @@index([status])
}

// Company model - Companies for job matching
model Company {
  id          Int      @id @default(autoincrement())
  name        String
  slug        String   @unique
  industry    String   // e.g., "tech"
  skills      String[] // Required skills
  logo        String?  // Company logo URL
  content     Json     // Page content
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  investments Investment[]

  @@index([slug])
  @@index([industry])
  @@index([skills])
  @@index([isActive])
}

// CV model - User CVs
model CV {
  id         Int      @id @default(autoincrement())
  userId     Int      @unique
  personal   Json     // Name, contact info
  education  Json[]   // Education history
  skills     String[] // Skills
  experience Json[]   // Work experience
  summary    String?  // Professional summary
  isPublic   Boolean  @default(false)
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([skills])
  @@index([isPublic])
}

// Portfolio model - User portfolios
model Portfolio {
  id          Int      @id @default(autoincrement())
  userId      Int
  title       String
  description String
  mediaUrls   String[] // Cloudinary URLs
  skills      String[] // Skills demonstrated
  projectUrl  String?  // Live project URL
  githubUrl   String?  // GitHub repository URL
  isPublished Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  user      User       @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([skills])
  @@index([isPublished])
}

// Product model - Marketplace products/services
model Product {
  id          Int      @id @default(autoincrement())
  userId      Int
  title       String
  description String
  price       Float
  currency    String   // e.g., "TZS", "USD"
  mediaUrls   String[] // Cloudinary URLs
  category    String   // e.g., "services", "products"
  location    String   // e.g., "Dar es Salaam, TZ"
  tags        String[] // Product tags
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  user      User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  orders    Order[]

  @@index([userId])
  @@index([category])
  @@index([location])
  @@index([tags])
  @@index([isActive])
  @@index([price])
}

// Order model - Product orders
model Order {
  id        Int      @id @default(autoincrement())
  buyerId   Int
  sellerId  Int
  productId Int
  quantity  Int      @default(1)
  totalAmount Float
  currency  String
  status    String   // e.g., "pending", "confirmed", "shipped", "delivered", "cancelled"
  notes     String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  buyer   User    @relation("UserOrders", fields: [buyerId], references: [id])
  seller  User    @relation("UserSales", fields: [sellerId], references: [id])
  product Product @relation(fields: [productId], references: [id])
  payment Payment?

  @@index([buyerId])
  @@index([sellerId])
  @@index([productId])
  @@index([status])
}

// FeedItem model - Main feed system
model FeedItem {
  id          Int      @id @default(autoincrement())
  type        String   // e.g., "portfolio", "product", "innovation", "provider", "club", "company", "academy", "achievement", "training", "event"
  contentId   Int      // ID of related content
  userId      Int?     // Optional user (for user-generated content)
  title       String
  description String?
  mediaUrls   String[] // Associated media
  tags        String[] // Content tags
  isPromoted  Boolean  @default(false) // For promoted/sponsored content
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  user         User?         @relation(fields: [userId], references: [id], onDelete: Cascade)
  interactions Interaction[]

  @@index([type])
  @@index([contentId])
  @@index([userId])
  @@index([createdAt])
  @@index([isPromoted])
  @@index([isActive])
  @@index([tags])
}

// Interaction model - Feed interactions
model Interaction {
  id         Int      @id @default(autoincrement())
  userId     Int
  feedItemId Int
  type       String   // e.g., "like", "comment", "share", "save"
  content    String?  // Comment text or additional data
  createdAt  DateTime @default(now())

  // Relations
  user     User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  feedItem FeedItem @relation(fields: [feedItemId], references: [id], onDelete: Cascade)

  @@unique([userId, feedItemId, type]) // Prevent duplicate interactions
  @@index([userId])
  @@index([feedItemId])
  @@index([type])
  @@index([createdAt])
}

// Payment model - Payment transactions
model Payment {
  id            Int      @id @default(autoincrement())
  userId        Int
  orderId       Int?     // Optional - can be standalone payment
  amount        Float
  currency      String   // e.g., "TZS", "USD"
  method        String   // e.g., "M-Pesa", "Stripe", "Airtel Money"
  status        String   // e.g., "pending", "completed", "failed", "refunded"
  transactionId String?  // External transaction ID
  metadata      Json?    // Additional payment data
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relations
  user  User   @relation(fields: [userId], references: [id])
  order Order? @relation(fields: [orderId], references: [id])

  @@unique([orderId]) // One payment per order
  @@index([userId])
  @@index([orderId])
  @@index([status])
  @@index([method])
  @@index([transactionId])
}

// Academy model - Sports academies
model Academy {
  id           Int      @id @default(autoincrement())
  name         String   // e.g., "Dar es Salaam Football Academy"
  slug         String   @unique
  sport        String   // e.g., "football", "basketball", "athletics"
  level        String   // e.g., "youth", "junior", "senior", "professional"
  description  String
  logo         String?  // Academy logo URL
  content      Json     // Page content (about, history, philosophy)
  location     String   // Physical location
  contactEmail String
  contactPhone String
  facilities   Json     // Available facilities and equipment
  programs     Json     // Training programs offered
  isActive     Boolean  @default(true)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  members      AcademyMember[]
  trainings    Training[]
  events       Event[]
  partnerships AcademyPartnership[]

  // New Relations for Five Pillars
  educationalPrograms EducationalProgram[]
  investments         Investment[]
  certifications      Certification[]
  techInnovations     TechInnovation[]
  infrastructures     Infrastructure[]

  @@index([slug])
  @@index([sport])
  @@index([level])
  @@index([location])
  @@index([isActive])
}

// AcademyMember model - Academy membership
model AcademyMember {
  id             Int       @id @default(autoincrement())
  userId         Int
  academyId      Int
  membershipType String    // e.g., "regular", "premium", "scholarship", "trial"
  status         String    // e.g., "active", "inactive", "suspended", "graduated"
  position       String?   // e.g., "goalkeeper", "striker", "midfielder"
  skillLevel     String    // e.g., "beginner", "intermediate", "advanced", "elite"
  talentVideos   Json      // Array of video URLs showcasing talent
  medicalInfo    Json?     // Medical records and health information
  emergencyContact Json    // Emergency contact details
  personalInfo   Json?     // Personal information (height, weight, etc.)
  joinedAt       DateTime  @default(now())
  expiresAt      DateTime? // Membership expiry date
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt

  // Relations
  user                User                  @relation(fields: [userId], references: [id], onDelete: Cascade)
  academy             Academy               @relation(fields: [academyId], references: [id])
  performances        Performance[]
  achievements        Achievement[]
  trainingAttendances TrainingAttendance[]
  eventParticipations EventParticipation[]
  programEnrollments  ProgramEnrollment[]

  @@unique([userId, academyId])
  @@index([userId])
  @@index([academyId])
  @@index([status])
  @@index([skillLevel])
  @@index([position])
}

// Performance model - Individual performance tracking
model Performance {
  id               Int      @id @default(autoincrement())
  academyMemberId  Int
  trainingId       Int?     // Optional - can be standalone performance record
  metricType       String   // e.g., "speed", "strength", "endurance", "technical_skill"
  value            Float    // Numeric value of the metric
  unit             String   // e.g., "seconds", "meters", "kg", "score"
  details          Json?    // Additional performance details and context
  recordedAt       DateTime @default(now())
  recordedBy       String   // Who recorded this performance (coach name/id)
  createdAt        DateTime @default(now())

  // Relations
  academyMember AcademyMember @relation(fields: [academyMemberId], references: [id], onDelete: Cascade)
  training      Training?     @relation(fields: [trainingId], references: [id])

  @@index([academyMemberId])
  @@index([trainingId])
  @@index([metricType])
  @@index([recordedAt])
}

// Achievement model - Awards and recognitions
model Achievement {
  id              Int       @id @default(autoincrement())
  academyMemberId Int
  eventId         Int?      // Optional - achievement from specific event
  type            String    // e.g., "award", "certificate", "milestone", "competition"
  title           String    // e.g., "Best Player of the Month"
  description     String
  level           String    // e.g., "academy", "regional", "national", "international"
  mediaUrls       String[]  // Photos, certificates, videos
  achievedAt      DateTime  @default(now())
  awardedBy       String    // Who awarded this achievement
  createdAt       DateTime  @default(now())

  // Relations
  academyMember AcademyMember @relation(fields: [academyMemberId], references: [id], onDelete: Cascade)
  event         Event?        @relation(fields: [eventId], references: [id])

  @@index([academyMemberId])
  @@index([eventId])
  @@index([type])
  @@index([level])
  @@index([achievedAt])
}

// Training model - Training sessions
model Training {
  id              Int      @id @default(autoincrement())
  academyId       Int
  title           String   // e.g., "Speed and Agility Training"
  description     String
  type            String   // e.g., "technical", "physical", "tactical", "mental"
  skillLevel      String   // e.g., "beginner", "intermediate", "advanced"
  scheduledAt     DateTime
  duration        Int      // Duration in minutes
  location        String   // Training location
  maxParticipants Int      // Maximum number of participants
  status          String   // e.g., "scheduled", "ongoing", "completed", "cancelled"
  drills          Json     // Training drills and exercises
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  academy     Academy               @relation(fields: [academyId], references: [id])
  attendances TrainingAttendance[]
  performances Performance[]

  @@index([academyId])
  @@index([scheduledAt])
  @@index([type])
  @@index([skillLevel])
  @@index([status])
}

// TrainingAttendance model - Track who attended training
model TrainingAttendance {
  id              Int      @id @default(autoincrement())
  trainingId      Int
  academyMemberId Int
  status          String   // e.g., "present", "absent", "late", "excused"
  performance     Json?    // Performance notes during training
  notes           String?  // Additional notes from coach
  createdAt       DateTime @default(now())

  // Relations
  training      Training      @relation(fields: [trainingId], references: [id], onDelete: Cascade)
  academyMember AcademyMember @relation(fields: [academyMemberId], references: [id], onDelete: Cascade)

  @@unique([trainingId, academyMemberId])
  @@index([trainingId])
  @@index([academyMemberId])
  @@index([status])
}

// Event model - Academy events and competitions
model Event {
  id           Int      @id @default(autoincrement())
  academyId    Int
  title        String   // e.g., "Inter-Academy Football Tournament"
  description  String
  type         String   // e.g., "competition", "showcase", "training_camp", "community"
  startDate    DateTime
  endDate      DateTime
  location     String
  requirements Json?    // Entry requirements and eligibility
  prizes       Json?    // Prize structure and rewards
  isPublic     Boolean  @default(true) // Whether event is open to public viewing
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  academy       Academy               @relation(fields: [academyId], references: [id])
  participants  EventParticipation[]
  achievements  Achievement[]

  @@index([academyId])
  @@index([startDate])
  @@index([type])
  @@index([isPublic])
}

// EventParticipation model - Track event participation and results
model EventParticipation {
  id              Int      @id @default(autoincrement())
  eventId         Int
  academyMemberId Int
  status          String   // e.g., "registered", "participating", "completed", "withdrawn"
  results         Json?    // Competition results and scores
  position        Int?     // Final position/ranking
  mediaUrls       String[] // Photos and videos from the event
  createdAt       DateTime @default(now())

  // Relations
  event         Event         @relation(fields: [eventId], references: [id], onDelete: Cascade)
  academyMember AcademyMember @relation(fields: [academyMemberId], references: [id], onDelete: Cascade)

  @@unique([eventId, academyMemberId])
  @@index([eventId])
  @@index([academyMemberId])
  @@index([status])
  @@index([position])
}

// AcademyPartnership model - Partnerships with clubs, companies, etc.
model AcademyPartnership {
  id              Int       @id @default(autoincrement())
  academyId       Int
  partnerId       Int       // ID of the partner (club, company, etc.)
  partnerType     String    // e.g., "club", "company", "provider", "institution"
  partnershipType String    // e.g., "scouting", "sponsorship", "equipment", "facility"
  terms           Json      // Partnership terms and conditions
  status          String    // e.g., "active", "inactive", "pending", "expired"
  startDate       DateTime  @default(now())
  endDate         DateTime?
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  // Relations
  academy Academy @relation(fields: [academyId], references: [id])

  @@index([academyId])
  @@index([partnerId])
  @@index([partnerType])
  @@index([partnershipType])
  @@index([status])
}

// ========================================
// PILLAR 1: FOOTBALL FOR EDUCATION & SKILLS DEVELOPMENT
// ========================================

// EducationalProgram model - Educational programs integrated with sports
model EducationalProgram {
  id              Int      @id @default(autoincrement())
  academyId       Int?     // Optional - can be academy-specific or general
  institutionId   Int?     // Optional - can be institution-specific
  title           String   // e.g., "Football & Mathematics", "Leadership Through Sports"
  description     String
  category        String   // e.g., "academic", "life_skills", "technical", "leadership"
  level           String   // e.g., "primary", "secondary", "tertiary", "professional"
  duration        Int      // Duration in hours
  curriculum      Json     // Detailed curriculum and learning objectives
  prerequisites   String[] // Required skills or knowledge
  learningOutcomes String[] // Expected learning outcomes
  mediaUrls       String[] // Educational materials, videos, documents
  isActive        Boolean  @default(true)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  academy         Academy?     @relation(fields: [academyId], references: [id])
  institution     Institution? @relation(fields: [institutionId], references: [id])
  enrollments     ProgramEnrollment[]
  certifications  Certification[]

  @@index([academyId])
  @@index([institutionId])
  @@index([category])
  @@index([level])
  @@index([isActive])
}

// ProgramEnrollment model - Track user enrollment in educational programs
model ProgramEnrollment {
  id                Int      @id @default(autoincrement())
  userId            Int
  programId         Int
  academyMemberId   Int?     // Optional - if enrolled through academy
  status            String   // e.g., "enrolled", "in_progress", "completed", "dropped"
  progress          Float    @default(0) // Progress percentage (0-100)
  startDate         DateTime @default(now())
  completionDate    DateTime?
  finalScore        Float?   // Final assessment score
  skillsAcquired    String[] // Skills gained through the program
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Relations
  user            User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  program         EducationalProgram @relation(fields: [programId], references: [id])
  academyMember   AcademyMember?    @relation(fields: [academyMemberId], references: [id])
  assessments     SkillAssessment[]

  @@unique([userId, programId])
  @@index([userId])
  @@index([programId])
  @@index([academyMemberId])
  @@index([status])
}

// SkillAssessment model - Track skill development and assessments
model SkillAssessment {
  id           Int      @id @default(autoincrement())
  enrollmentId Int
  skillType    String   // e.g., "technical", "academic", "leadership", "teamwork"
  skillName    String   // e.g., "ball_control", "mathematics", "communication"
  initialScore Float?   // Initial assessment score
  currentScore Float    // Current skill level score
  targetScore  Float?   // Target score to achieve
  assessedBy   String   // Who conducted the assessment
  assessmentDate DateTime @default(now())
  notes        String?  // Assessment notes and feedback
  mediaUrls    String[] // Evidence of skill demonstration
  createdAt    DateTime @default(now())

  // Relations
  enrollment ProgramEnrollment @relation(fields: [enrollmentId], references: [id], onDelete: Cascade)

  @@index([enrollmentId])
  @@index([skillType])
  @@index([skillName])
  @@index([assessmentDate])
}

// Certification model - Digital certificates and credentials
model Certification {
  id              Int      @id @default(autoincrement())
  userId          Int
  programId       Int
  academyId       Int?     // Optional - if issued by academy
  institutionId   Int?     // Optional - if issued by institution
  title           String   // e.g., "Football Coaching Level 1", "Sports Leadership Certificate"
  description     String
  level           String   // e.g., "basic", "intermediate", "advanced", "expert"
  certificateUrl  String   // URL to digital certificate
  verificationCode String  @unique // Unique code for verification
  skillsValidated String[] // Skills validated by this certification
  issuedAt        DateTime @default(now())
  expiresAt       DateTime? // Optional expiry date
  isActive        Boolean  @default(true)
  createdAt       DateTime @default(now())

  // Relations
  user        User               @relation(fields: [userId], references: [id], onDelete: Cascade)
  program     EducationalProgram @relation(fields: [programId], references: [id])
  academy     Academy?           @relation(fields: [academyId], references: [id])
  institution Institution?       @relation(fields: [institutionId], references: [id])

  @@index([userId])
  @@index([programId])
  @@index([academyId])
  @@index([institutionId])
  @@index([verificationCode])
  @@index([isActive])
}

// ========================================
// PILLAR 2: TRADE & INVESTMENT FACILITATION
// ========================================

// Investment model - Investment opportunities and funding
model Investment {
  id              Int      @id @default(autoincrement())
  userId          Int      // Investor or investment seeker
  academyId       Int?     // Optional - academy-related investment
  companyId       Int?     // Optional - company investment
  title           String   // e.g., "Youth Football Academy Expansion"
  description     String
  type            String   // e.g., "equity", "loan", "grant", "sponsorship", "partnership"
  category        String   // e.g., "sports_infrastructure", "technology", "agriculture", "education"
  amount          Float    // Investment amount
  currency        String   // e.g., "TZS", "USD"
  targetAmount    Float?   // Target funding amount
  raisedAmount    Float    @default(0) // Amount raised so far
  status          String   // e.g., "seeking", "funded", "active", "completed", "cancelled"
  riskLevel       String   // e.g., "low", "medium", "high"
  expectedReturn  Float?   // Expected return percentage
  timeline        String   // Investment timeline
  businessPlan    Json?    // Business plan and projections
  mediaUrls       String[] // Pitch decks, videos, documents
  isPublic        Boolean  @default(true) // Whether visible to public
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  user            User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  academy         Academy?          @relation(fields: [academyId], references: [id])
  company         Company?          @relation(fields: [companyId], references: [id])
  applications    InvestmentApplication[]
  updates         InvestmentUpdate[]

  @@index([userId])
  @@index([academyId])
  @@index([companyId])
  @@index([type])
  @@index([category])
  @@index([status])
  @@index([isPublic])
}

// InvestmentApplication model - Investment applications and proposals
model InvestmentApplication {
  id           Int      @id @default(autoincrement())
  investmentId Int
  investorId   Int      // User applying to invest
  amount       Float    // Proposed investment amount
  currency     String
  terms        Json     // Investment terms and conditions
  proposal     String   // Investment proposal
  status       String   // e.g., "pending", "approved", "rejected", "negotiating"
  notes        String?  // Additional notes
  appliedAt    DateTime @default(now())
  respondedAt  DateTime?
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  investment Investment @relation(fields: [investmentId], references: [id], onDelete: Cascade)
  investor   User       @relation(fields: [investorId], references: [id], onDelete: Cascade)

  @@unique([investmentId, investorId])
  @@index([investmentId])
  @@index([investorId])
  @@index([status])
}

// InvestmentUpdate model - Progress updates on investments
model InvestmentUpdate {
  id           Int      @id @default(autoincrement())
  investmentId Int
  title        String   // e.g., "Q1 Progress Report"
  content      String   // Update content
  type         String   // e.g., "progress", "milestone", "financial", "challenge"
  metrics      Json?    // Performance metrics and KPIs
  mediaUrls    String[] // Supporting documents, images, videos
  isPublic     Boolean  @default(true) // Whether visible to all investors
  createdAt    DateTime @default(now())

  // Relations
  investment Investment @relation(fields: [investmentId], references: [id], onDelete: Cascade)

  @@index([investmentId])
  @@index([type])
  @@index([createdAt])
}

// BusinessPartnership model - Business partnerships and collaborations
model BusinessPartnership {
  id              Int       @id @default(autoincrement())
  initiatorId     Int       // User who initiated the partnership
  partnerId       Int       // Partner user/entity ID
  partnerType     String    // e.g., "user", "academy", "company", "institution"
  partnershipType String    // e.g., "supplier", "distributor", "joint_venture", "strategic"
  title           String    // Partnership title
  description     String
  terms           Json      // Partnership terms and agreements
  benefits        Json      // Expected benefits for both parties
  status          String    // e.g., "proposed", "negotiating", "active", "completed", "terminated"
  startDate       DateTime?
  endDate         DateTime?
  value           Float?    // Partnership value
  currency        String?
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  // Relations
  initiator User @relation("InitiatedPartnerships", fields: [initiatorId], references: [id], onDelete: Cascade)
  partner   User @relation("ReceivedPartnerships", fields: [partnerId], references: [id], onDelete: Cascade)

  @@index([initiatorId])
  @@index([partnerId])
  @@index([partnerType])
  @@index([partnershipType])
  @@index([status])
}

// ========================================
// PILLAR 3: AGRO-PROCESSING & SPORTS NUTRITION
// ========================================

// NutritionProduct model - Sports nutrition and agro-processed products
model NutritionProduct {
  id              Int      @id @default(autoincrement())
  userId          Int      // Product creator/seller
  title           String   // e.g., "High-Protein Energy Bar", "Organic Sports Drink"
  description     String
  category        String   // e.g., "supplements", "energy_drinks", "protein_bars", "organic_foods"
  type            String   // e.g., "pre_workout", "post_workout", "recovery", "general_nutrition"
  ingredients     Json     // Detailed ingredient list and nutritional info
  nutritionFacts  Json     // Calories, protein, carbs, fats, vitamins, etc.
  targetAudience  String[] // e.g., ["athletes", "youth", "professionals", "general"]
  certifications  String[] // e.g., ["organic", "halal", "vegan", "gluten_free"]
  price           Float
  currency        String
  unit            String   // e.g., "per_bottle", "per_kg", "per_pack"
  mediaUrls       String[] // Product images, videos, certificates
  supplierInfo    Json?    // Information about suppliers and sourcing
  isActive        Boolean  @default(true)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  user            User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  orders          NutritionOrder[]
  reviews         ProductReview[]
  supplyChain     SupplyChainLink[]

  @@index([userId])
  @@index([category])
  @@index([type])
  @@index([targetAudience])
  @@index([isActive])
}

// NutritionOrder model - Orders for nutrition products
model NutritionOrder {
  id              Int      @id @default(autoincrement())
  buyerId         Int
  sellerId        Int
  productId       Int
  quantity        Int
  unitPrice       Float
  totalAmount     Float
  currency        String
  deliveryAddress Json     // Delivery address details
  status          String   // e.g., "pending", "confirmed", "processing", "shipped", "delivered"
  orderDate       DateTime @default(now())
  deliveryDate    DateTime?
  notes           String?
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  buyer   User            @relation("NutritionBuyer", fields: [buyerId], references: [id])
  seller  User            @relation("NutritionSeller", fields: [sellerId], references: [id])
  product NutritionProduct @relation(fields: [productId], references: [id])

  @@index([buyerId])
  @@index([sellerId])
  @@index([productId])
  @@index([status])
}

// SupplyChainLink model - Track supply chain for agro-processing
model SupplyChainLink {
  id          Int      @id @default(autoincrement())
  productId   Int
  supplierId  Int      // User ID of supplier
  linkType    String   // e.g., "farmer", "processor", "distributor", "retailer"
  description String   // Description of this link in the chain
  location    String   // Geographic location
  processes   Json     // Processes performed at this link
  quality     Json?    // Quality metrics and certifications
  timeline    Json     // Processing timeline and dates
  isVerified  Boolean  @default(false) // Whether this link is verified
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  product  NutritionProduct @relation(fields: [productId], references: [id], onDelete: Cascade)
  supplier User             @relation(fields: [supplierId], references: [id])

  @@index([productId])
  @@index([supplierId])
  @@index([linkType])
  @@index([location])
}

// ProductReview model - Reviews for nutrition products
model ProductReview {
  id        Int      @id @default(autoincrement())
  productId Int
  userId    Int
  rating    Float    // Rating out of 5
  title     String?
  content   String
  verified  Boolean  @default(false) // Whether reviewer actually purchased
  helpful   Int      @default(0) // Helpful votes count
  mediaUrls String[] // Review images/videos
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  product NutritionProduct @relation(fields: [productId], references: [id], onDelete: Cascade)
  user    User             @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([productId, userId])
  @@index([productId])
  @@index([userId])
  @@index([rating])
  @@index([verified])
}

// ========================================
// PILLAR 4: NETWORKING & DIPLOMATIC BRIDGES
// ========================================

// InternationalNetwork model - Cross-border networking and connections
model InternationalNetwork {
  id              Int      @id @default(autoincrement())
  userId          Int
  networkType     String   // e.g., "diplomatic", "business", "sports", "cultural", "academic"
  title           String   // e.g., "East Africa Football Diplomacy Network"
  description     String
  countries       String[] // ISO country codes of participating countries
  objectives      String[] // Network objectives and goals
  activities      Json     // Planned and ongoing activities
  membershipType  String   // e.g., "open", "invitation_only", "verified_only"
  isActive        Boolean  @default(true)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  creator     User                    @relation(fields: [userId], references: [id], onDelete: Cascade)
  memberships NetworkMembership[]
  events      DiplomaticEvent[]
  exchanges   CulturalExchange[]

  @@index([userId])
  @@index([networkType])
  @@index([countries])
  @@index([isActive])
}

// NetworkMembership model - Membership in international networks
model NetworkMembership {
  id        Int      @id @default(autoincrement())
  networkId Int
  userId    Int
  role      String   // e.g., "member", "coordinator", "ambassador", "observer"
  status    String   // e.g., "active", "pending", "suspended", "alumni"
  joinedAt  DateTime @default(now())
  expertise String[] // Areas of expertise
  interests String[] // Areas of interest
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  network InternationalNetwork @relation(fields: [networkId], references: [id], onDelete: Cascade)
  user    User                 @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([networkId, userId])
  @@index([networkId])
  @@index([userId])
  @@index([role])
  @@index([status])
}

// DiplomaticEvent model - Diplomatic and cultural events
model DiplomaticEvent {
  id              Int      @id @default(autoincrement())
  networkId       Int
  organizerId     Int
  title           String   // e.g., "Tanzania-Kenya Youth Football Exchange"
  description     String
  type            String   // e.g., "summit", "exchange", "tournament", "conference", "workshop"
  startDate       DateTime
  endDate         DateTime
  location        String   // Physical or virtual location
  countries       String[] // Participating countries
  objectives      String[] // Event objectives
  expectedOutcomes String[] // Expected outcomes
  agenda          Json     // Event agenda and schedule
  participants    Json?    // Participant information
  outcomes        Json?    // Actual outcomes and results
  mediaUrls       String[] // Event photos, videos, documents
  isPublic        Boolean  @default(true)
  status          String   // e.g., "planned", "ongoing", "completed", "cancelled"
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  network   InternationalNetwork @relation(fields: [networkId], references: [id])
  organizer User                 @relation(fields: [organizerId], references: [id])

  @@index([networkId])
  @@index([organizerId])
  @@index([type])
  @@index([startDate])
  @@index([countries])
  @@index([status])
}

// CulturalExchange model - Cultural exchange programs
model CulturalExchange {
  id              Int      @id @default(autoincrement())
  networkId       Int
  coordinatorId   Int
  title           String   // e.g., "Football Culture Exchange Program"
  description     String
  type            String   // e.g., "student_exchange", "coach_exchange", "cultural_immersion"
  duration        Int      // Duration in days
  sourceCountry   String   // ISO country code
  targetCountry   String   // ISO country code
  maxParticipants Int
  requirements    Json     // Participation requirements
  benefits        String[] // Program benefits
  curriculum      Json?    // Exchange curriculum
  cost            Float?   // Program cost
  currency        String?
  applicationDeadline DateTime?
  startDate       DateTime
  endDate         DateTime
  status          String   // e.g., "open", "closed", "ongoing", "completed"
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  network     InternationalNetwork @relation(fields: [networkId], references: [id])
  coordinator User                 @relation(fields: [coordinatorId], references: [id])
  applications ExchangeApplication[]

  @@index([networkId])
  @@index([coordinatorId])
  @@index([type])
  @@index([sourceCountry])
  @@index([targetCountry])
  @@index([status])
}

// ExchangeApplication model - Applications for cultural exchange programs
model ExchangeApplication {
  id          Int      @id @default(autoincrement())
  exchangeId  Int
  applicantId Int
  motivation  String   // Why they want to participate
  experience  Json     // Relevant experience and background
  references  Json     // References and recommendations
  documents   String[] // Supporting documents (CV, certificates, etc.)
  status      String   // e.g., "submitted", "under_review", "accepted", "rejected", "waitlisted"
  appliedAt   DateTime @default(now())
  reviewedAt  DateTime?
  feedback    String?  // Feedback from reviewers
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  exchange  CulturalExchange @relation(fields: [exchangeId], references: [id], onDelete: Cascade)
  applicant User             @relation(fields: [applicantId], references: [id], onDelete: Cascade)

  @@unique([exchangeId, applicantId])
  @@index([exchangeId])
  @@index([applicantId])
  @@index([status])
}

// ========================================
// PILLAR 5: TECHNOLOGY & INFRASTRUCTURE GROWTH
// ========================================

// TechInnovation model - Technology innovations and solutions
model TechInnovation {
  id              Int      @id @default(autoincrement())
  userId          Int
  academyId       Int?     // Optional - academy-related innovation
  institutionId   Int?     // Optional - institution-related innovation
  title           String   // e.g., "AI-Powered Performance Analytics", "Smart Stadium System"
  description     String
  category        String   // e.g., "sports_tech", "infrastructure", "mobile_app", "iot", "ai_ml"
  stage           String   // e.g., "concept", "prototype", "testing", "production", "deployed"
  techStack       String[] // Technologies used
  features        Json     // Key features and capabilities
  benefits        String[] // Benefits and value proposition
  targetUsers     String[] // Target user groups
  businessModel   Json?    // Business model and monetization
  mediaUrls       String[] // Demos, screenshots, videos
  githubUrl       String?  // Code repository
  demoUrl         String?  // Live demo URL
  isOpenSource    Boolean  @default(false)
  isCommercial    Boolean  @default(false)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  user        User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  academy     Academy?     @relation(fields: [academyId], references: [id])
  institution Institution? @relation(fields: [institutionId], references: [id])
  adoptions   TechAdoption[]

  @@index([userId])
  @@index([academyId])
  @@index([institutionId])
  @@index([category])
  @@index([stage])
  @@index([isOpenSource])
}

// TechAdoption model - Track adoption of technology innovations
model TechAdoption {
  id           Int      @id @default(autoincrement())
  innovationId Int
  adopterId    Int      // User/organization adopting the technology
  adopterType  String   // e.g., "academy", "institution", "company", "individual"
  status       String   // e.g., "interested", "testing", "implementing", "deployed", "discontinued"
  feedback     String?  // Feedback on the technology
  metrics      Json?    // Usage metrics and performance data
  adoptedAt    DateTime @default(now())
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  innovation TechInnovation @relation(fields: [innovationId], references: [id], onDelete: Cascade)
  adopter    User           @relation(fields: [adopterId], references: [id], onDelete: Cascade)

  @@unique([innovationId, adopterId])
  @@index([innovationId])
  @@index([adopterId])
  @@index([adopterType])
  @@index([status])
}

// Infrastructure model - Infrastructure development projects
model Infrastructure {
  id              Int      @id @default(autoincrement())
  userId          Int      // Project initiator
  academyId       Int?     // Optional - academy infrastructure
  title           String   // e.g., "Modern Football Training Facility", "Digital Sports Complex"
  description     String
  type            String   // e.g., "sports_facility", "technology_center", "training_ground", "stadium"
  location        String   // Geographic location
  scope           Json     // Project scope and specifications
  budget          Float    // Total project budget
  currency        String
  fundingStatus   String   // e.g., "seeking", "partial", "fully_funded", "completed"
  timeline        Json     // Project timeline and milestones
  status          String   // e.g., "planning", "approved", "construction", "completed", "operational"
  sustainability  Json?    // Sustainability features and certifications
  technology      Json?    // Technology components and smart features
  community       Json?    // Community impact and benefits
  mediaUrls       String[] // Plans, renderings, progress photos
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  user     User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  academy  Academy?  @relation(fields: [academyId], references: [id])
  updates  InfrastructureUpdate[]

  @@index([userId])
  @@index([academyId])
  @@index([type])
  @@index([location])
  @@index([status])
  @@index([fundingStatus])
}

// InfrastructureUpdate model - Progress updates on infrastructure projects
model InfrastructureUpdate {
  id               Int      @id @default(autoincrement())
  infrastructureId Int
  title            String   // e.g., "Foundation Complete", "50% Construction Progress"
  content          String   // Update content
  type             String   // e.g., "milestone", "progress", "delay", "completion"
  progress         Float    // Progress percentage (0-100)
  budget           Json?    // Budget updates and spending
  timeline         Json?    // Timeline updates
  challenges       String[] // Challenges faced
  mediaUrls        String[] // Progress photos, videos, documents
  isPublic         Boolean  @default(true)
  createdAt        DateTime @default(now())

  // Relations
  infrastructure Infrastructure @relation(fields: [infrastructureId], references: [id], onDelete: Cascade)

  @@index([infrastructureId])
  @@index([type])
  @@index([createdAt])
}
